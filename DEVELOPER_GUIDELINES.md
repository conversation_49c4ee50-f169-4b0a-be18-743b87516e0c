# Developer Guidelines

## Table of Contents
1. [Project Overview](#project-overview)
2. [Architecture Overview](#architecture-overview)
3. [Project Structure](#project-structure)
4. [Design Patterns](#design-patterns)
5. [Coding Standards](#coding-standards)
6. [Testing Guidelines](#testing-guidelines)
7. [Development Workflow](#development-workflow)
8. [Adding New Features](#adding-new-features)

## Project Overview

**Bill Splitter 2** is a Flutter application for splitting bills among multiple people. The app follows clean architecture principles with clear separation of concerns, dependency injection, and comprehensive testing.

### Key Technologies
- **Framework**: Flutter 3.8.1+
- **State Management**: ChangeNotifier with custom state classes
- **Dependency Injection**: Custom Locator pattern (singleton)
- **Local Storage**: Flutter Secure Storage
- **Navigation**: GoRouter
- **Testing**: flutter_test + mocktail
- **Linting**: flutter_lints

## Architecture Overview

The application follows a **layered architecture** with clear separation of concerns:

```
┌─────────────────┐
│   Presentation  │ ← Builders, Views, Components
├─────────────────┤
│   Application   │ ← States, Navigation
├─────────────────┤
│   Domain        │ ← Models, Business Rules
├─────────────────┤
│   Infrastructure│ ← Repositories, Services
└─────────────────┘
```

### Core Principles
1. **Dependency Inversion**: High-level modules don't depend on low-level modules
2. **Single Responsibility**: Each class has one reason to change
3. **Interface Segregation**: Abstract interfaces define contracts
4. **Fire-and-Forget Storage**: Storage operations don't block UI or throw errors

## Project Structure

```
lib/
├── app/                    # Application-level configuration
│   ├── builders/          # App-level widget builders
│   └── states/            # App-level state management
├── bill/                  # Bill splitting domain
│   └── items/
│       ├── builders/      # Item-related widget builders
│       ├── models/        # Data models (Item, Tip)
│       ├── repositories/  # Data access layer
│       ├── rules/         # Business rules documentation
│       ├── states/        # State management
│       └── views/         # UI components
├── components/            # Reusable UI components
├── local_storage/         # Local storage abstraction
├── localization/          # Internationalization
├── locator/              # Dependency injection
├── navigation/           # Routing configuration
├── theme/                # Theme management
├── unique_identifier/    # ID generation service
└── utils/                # Utility functions
```

### Folder Naming Conventions
- **Plural nouns** for feature folders (`items/`, `components/`)
- **Descriptive names** for purpose-specific folders (`builders/`, `states/`)
- **Lowercase with underscores** for file names (`item_repository.dart`)

## Design Patterns

### 1. Repository Pattern
**Purpose**: Abstracts data access logic from business logic.

**Structure**:
```dart
// Abstract interface
abstract class ItemRepository {
  Future<List<Item>> getItemList();
  Future<void> createItem(Item item);
}

// Concrete implementation
class ItemRepositoryImplementation implements ItemRepository {
  final LocalStorageService _localStorageService;
  // Implementation details...
}
```

### 2. State Pattern with ChangeNotifier
**Purpose**: Manages UI state and business logic separation.

**Structure**:
```dart
// Abstract state interface
abstract class ItemDefinitionState extends ChangeNotifier {
  List<Item> get itemList;
  void onItemNameChanged(String itemId, String name);
}

// Concrete implementation
class ItemDefinitionStateImplementation extends ChangeNotifier 
    implements ItemDefinitionState {
  // State management implementation...
}
```

### 3. Dependency Injection (Locator Pattern)
**Purpose**: Manages object creation and dependencies.

**Usage**:
```dart
// Registration (in main.dart)
Locator.instance.registerLazySingleton<ItemRepository>(
  () => ItemRepositoryImplementation(
    localStorageService: Locator.instance.get<LocalStorageService>(),
  ),
);

// Consumption
final state = Locator.instance.get<ItemDefinitionState>();
```

### 4. Builder Pattern for Widgets
**Purpose**: Separates widget construction from business logic.

**Structure**:
```dart
class ItemDefinitionBuilder extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final state = Locator.instance.get<ItemDefinitionState>();
    return ListenableBuilder(
      listenable: state,
      builder: (context, child) => ItemDefinitionView(state: state),
    );
  }
}
```

## Coding Standards

### File Organization
1. **Imports**: Flutter imports first, then package imports, then relative imports
2. **Class Structure**: Public members first, then private members
3. **Method Order**: Constructors, getters/setters, public methods, private methods

### Naming Conventions
- **Classes**: PascalCase (`ItemRepository`)
- **Files**: snake_case (`item_repository.dart`)
- **Variables/Methods**: camelCase (`itemList`, `onItemNameChanged`)
- **Constants**: SCREAMING_SNAKE_CASE (`DEFAULT_QUANTITY`)
- **Private members**: Leading underscore (`_itemList`)

### Code Style
- **Line length**: Maximum 80 characters
- **Indentation**: 2 spaces
- **Trailing commas**: Always use for multi-line parameter lists
- **Documentation**: Document public APIs with dartdoc comments

### Error Handling
- **Storage operations**: Fire-and-forget with `.ignore()`
- **Validation**: Return error messages, don't throw exceptions
- **State errors**: Use `StateError` for invalid state transitions
- **Argument errors**: Use `ArgumentError` for invalid parameters

## Testing Guidelines

### Testing Philosophy
- **Test-Driven Development (TDD)**: Write tests before implementation
- **Separation by Type**: Repository tests separate from state tests
- **Comprehensive Coverage**: Unit tests, widget tests, and integration tests

### Test Structure
```
test/
├── bill/items/
│   ├── repositories/          # Repository implementation tests
│   ├── states/               # State implementation tests
│   └── views/                # Widget tests
├── locator/                  # Dependency injection tests
└── local_storage/            # Storage service tests
```

### Testing Patterns

#### Repository Tests
```dart
class MockLocalStorageService extends Mock implements LocalStorageService {
  @override
  Future<void> setValue<T>(LocalStorageKey key, T value) {
    return super.noSuchMethod(
      Invocation.method(#setValue, [key, value]),
    ) ?? Future<void>.value();
  }
}
```

#### State Tests
```dart
setUp(() {
  mockItemRepository = MockItemRepository();
  // Set up default mock behaviors
  when(() => mockItemRepository.getItemList())
      .thenAnswer((_) async => []);
});
```

#### Widget Tests
```dart
testWidgets('should show validation errors when continue is pressed', 
    (WidgetTester tester) async {
  // Arrange, Act, Assert pattern
});
```

### Mock Setup
- **Use mocktail** for mocking dependencies
- **Register fallback values** for complex types with `registerFallbackValue()`
- **Default behaviors** in setUp() for common mock responses
- **Specific behaviors** in individual tests for test-specific scenarios

## Development Workflow

### Before Starting Development
1. **Understand the feature**: Review business rules in `rules.md`
2. **Check existing patterns**: Look for similar implementations
3. **Plan the architecture**: Identify required repositories, states, and views

### Development Process
1. **Write tests first** (TDD approach)
2. **Implement interfaces** before concrete classes
3. **Register dependencies** in `main.dart`
4. **Update documentation** if adding new patterns

### Code Review Checklist
- [ ] Tests written and passing
- [ ] Follows established patterns
- [ ] Dependencies properly injected
- [ ] Error handling implemented
- [ ] Documentation updated
- [ ] Linting passes (`flutter analyze`)

## Adding New Features

### 1. Define the Domain
- Create models in `models/` folder
- Document business rules in `rules/` folder
- Define validation requirements

### 2. Create Repository Layer
```dart
// 1. Define abstract interface
abstract class NewFeatureRepository {
  Future<List<NewModel>> getItems();
}

// 2. Implement concrete class
class NewFeatureRepositoryImplementation implements NewFeatureRepository {
  final LocalStorageService _localStorageService;
  // Implementation...
}

// 3. Register in main.dart
Locator.instance.registerLazySingleton<NewFeatureRepository>(
  () => NewFeatureRepositoryImplementation(
    localStorageService: Locator.instance.get<LocalStorageService>(),
  ),
);
```

### 3. Create State Management
```dart
// 1. Define abstract state
abstract class NewFeatureState extends ChangeNotifier {
  List<NewModel> get items;
  void onItemChanged(String id, String value);
}

// 2. Implement state class
class NewFeatureStateImplementation extends ChangeNotifier 
    implements NewFeatureState {
  final NewFeatureRepository _repository;
  // Implementation with fire-and-forget storage...
}
```

### 4. Create UI Layer
```dart
// 1. Builder widget
class NewFeatureBuilder extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final state = Locator.instance.get<NewFeatureState>();
    return ListenableBuilder(
      listenable: state,
      builder: (context, child) => NewFeatureView(state: state),
    );
  }
}

// 2. View widget
class NewFeatureView extends StatelessWidget {
  final NewFeatureState state;
  // UI implementation...
}
```

### 5. Add Navigation
Update `navigation_builder.dart` with new routes:
```dart
GoRoute(
  path: '/new-feature',
  builder: (context, state) => NewFeatureBuilder(),
),
```

### 6. Write Comprehensive Tests
- Repository implementation tests
- State implementation tests  
- Widget tests for UI components
- Integration tests for complete workflows

---

**Remember**: This codebase values consistency, testability, and maintainability. When in doubt, follow existing patterns and ask for guidance during code review.
