import 'package:bill_splitter_2/local_storage/models/local_storage_key.dart';
import 'package:bill_splitter_2/local_storage/services/local_storage_service.dart';
import 'package:bill_splitter_2/localization/repositories/locale_repository_implementation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockLocalStorageService extends Mock implements LocalStorageService {}

void main() {
  late MockLocalStorageService mockLocalStorageService;
  late LocaleRepositoryImplementation localeRepository;

  setUpAll(() {
    registerFallbackValue(LocalStorageKey.locale);
  });

  setUp(() {
    mockLocalStorageService = MockLocalStorageService();
    localeRepository = LocaleRepositoryImplementation(
      localStorageService: mockLocalStorageService,
    );
  });

  group('LocaleRepositoryImplementation', () {
    group('initialization', () {
      test('initializes with default locale when not provided', () {
        // Act
        final repository = LocaleRepositoryImplementation(
          localStorageService: mockLocalStorageService,
        );

        // Assert
        expect(repository.currentLocale.languageCode, equals('en'));
        expect(repository.currentLocale.countryCode, isNull);
      });

      test('initializes with provided locale', () {
        // Arrange
        final initialLocale = Locale('pt', 'BR');

        // Act
        final repository = LocaleRepositoryImplementation(
          localStorageService: mockLocalStorageService,
          initialLocale: initialLocale,
        );

        // Assert
        expect(repository.currentLocale.languageCode, equals('pt'));
        expect(repository.currentLocale.countryCode, equals('BR'));
      });
    });

    group('currentLocale', () {
      test('returns current locale', () {
        // Assert
        expect(localeRepository.currentLocale.languageCode, equals('en'));
      });
    });

    group('setLocale', () {
      test('updates current locale and saves to storage', () async {
        // Arrange
        final newLocale = Locale('es', 'MX');
        when(() => mockLocalStorageService.setValue(
              LocalStorageKey.locale,
              'es_MX',
            )).thenAnswer((_) => Future<void>.value());

        var listenerCalled = false;
        localeRepository.addListener(() {
          listenerCalled = true;
        });

        // Act
        await localeRepository.setLocale(newLocale);

        // Assert
        expect(localeRepository.currentLocale.languageCode, equals('es'));
        expect(localeRepository.currentLocale.countryCode, equals('MX'));
        expect(listenerCalled, isTrue);
        verify(() => mockLocalStorageService.setValue(
              LocalStorageKey.locale,
              'es_MX',
            )).called(1);
      });

      test('saves locale without country code', () async {
        // Arrange
        final newLocale = Locale('fr');
        when(() => mockLocalStorageService.setValue(
              LocalStorageKey.locale,
              'fr',
            )).thenAnswer((_) => Future<void>.value());

        // Act
        await localeRepository.setLocale(newLocale);

        // Assert
        verify(() => mockLocalStorageService.setValue(
              LocalStorageKey.locale,
              'fr',
            )).called(1);
      });
    });

    group('loadSavedLocale', () {
      test('loads and sets saved locale from storage', () async {
        // Arrange
        when(() => mockLocalStorageService.getValue<String>(
              LocalStorageKey.locale,
            )).thenAnswer((_) async => 'pt_BR');

        // Act
        await localeRepository.loadSavedLocale();

        // Assert
        expect(localeRepository.currentLocale.languageCode, equals('pt'));
        expect(localeRepository.currentLocale.countryCode, equals('BR'));
        verify(() => mockLocalStorageService.getValue<String>(
              LocalStorageKey.locale,
            )).called(1);
      });

      test('keeps current locale when no saved locale exists', () async {
        // Arrange
        when(() => mockLocalStorageService.getValue<String>(
              LocalStorageKey.locale,
            )).thenAnswer((_) async => null);

        // Act
        await localeRepository.loadSavedLocale();

        // Assert
        expect(localeRepository.currentLocale.languageCode, equals('en'));
        verify(() => mockLocalStorageService.getValue<String>(
              LocalStorageKey.locale,
            )).called(1);
      });

      test('handles locale without country code', () async {
        // Arrange
        when(() => mockLocalStorageService.getValue<String>(
              LocalStorageKey.locale,
            )).thenAnswer((_) async => 'fr');

        // Act
        await localeRepository.loadSavedLocale();

        // Assert
        expect(localeRepository.currentLocale.languageCode, equals('fr'));
        expect(localeRepository.currentLocale.countryCode, isNull);
      });
    });

    group('strings', () {
      test('returns English strings for en locale', () {
        // Arrange
        final repository = LocaleRepositoryImplementation(
          localStorageService: mockLocalStorageService,
          initialLocale: const Locale('en'),
        );

        // Act & Assert
        expect(repository.strings, isNotNull);
      });

      test('returns English strings for unsupported locale', () {
        // Arrange
        final repository = LocaleRepositoryImplementation(
          localStorageService: mockLocalStorageService,
          initialLocale: const Locale('unsupported'),
        );

        // Act & Assert
        expect(repository.strings, isNotNull);
      });
    });
  });
}
