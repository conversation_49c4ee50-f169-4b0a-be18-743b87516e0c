import 'package:bill_splitter_2/bill/items/models/item.dart';
import 'package:bill_splitter_2/bill/items/models/tip.dart';
import 'package:bill_splitter_2/bill/items/repositories/item_repository.dart';
import 'package:bill_splitter_2/bill/items/repositories/tip_repository.dart';
import 'package:bill_splitter_2/bill/items/states/item_definition_state_implementation.dart';
import 'package:bill_splitter_2/localization/repositories/locale_repository.dart';
import 'package:bill_splitter_2/localization/repositories/strings.dart';
import 'package:bill_splitter_2/utils/validators.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockItemRepository extends Mock implements ItemRepository {}

class MockTipRepository extends Mock implements TipRepository {}

class MockLocaleRepository extends Mock implements LocaleRepository {}

class MockValidators extends Mock implements Validators {}

class MockStrings extends Mock implements Strings {}

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  late MockItemRepository mockItemRepository;
  late MockTipRepository mockTipRepository;
  late LocaleRepository mockLocaleRepository;
  late Validators mockValidators;
  late MockStrings mockStrings;
  late ItemDefinitionStateImplementation state;

  setUpAll(() {
    registerFallbackValue(Tip());
    registerFallbackValue(Item(id: 'itemId'));
  });

  setUp(() {
    mockItemRepository = MockItemRepository();
    mockTipRepository = MockTipRepository();
    mockLocaleRepository = MockLocaleRepository();
    mockValidators = MockValidators();
    mockStrings = MockStrings();

    // Default mock setups
    when(
      () => mockItemRepository.generateItem(),
    ).thenReturn(Item(id: 'new-item-id'));
    when(
      () => mockItemRepository.createItem(any()),
    ).thenAnswer((_) async => []);
    when(() => mockItemRepository.getItemList()).thenAnswer((_) async => []);
    when(
      () => mockItemRepository.updateItem(any()),
    ).thenAnswer((_) async => []);
    when(
      () => mockItemRepository.deleteItem(any()),
    ).thenAnswer((_) async => []);

    when(
      () => mockTipRepository.checkIfTipHasBeenInitialized(),
    ).thenAnswer((_) async => false);
    when(
      () => mockTipRepository.createTip(any()),
    ).thenAnswer((_) async => Tip(percentage: 15));
    when(
      () => mockTipRepository.getTip(),
    ).thenAnswer((_) async => Tip(percentage: 15));
    when(() => mockTipRepository.updateTip(any())).thenAnswer((_) async {});
    when(() => mockTipRepository.deleteTip()).thenAnswer((_) async {});

    // Mock strings setup
    when(() => mockLocaleRepository.strings).thenReturn(mockStrings);
    when(() => mockStrings.itemDeleteForbiddenError).thenReturn('Cannot delete the only item');
    when(() => mockStrings.itemIdNotFoundError(any())).thenReturn('Item not found');
  });

  group('ItemDefinitionStateImplementation', () {
    group('Initialization', () {
      test('creates default item when no items exist in storage', () async {
        // Arrange
        when(
          () => mockItemRepository.getItemList(),
        ).thenAnswer((_) async => []);
        when(
          () => mockItemRepository.generateItem(),
        ).thenReturn(Item(id: 'new-item-id'));
        when(
          () => mockItemRepository.createItem(any()),
        ).thenAnswer((_) async => []);
        when(
          () => mockTipRepository.checkIfTipHasBeenInitialized(),
        ).thenAnswer((_) async => false);
        when(
          () => mockTipRepository.createTip(any()),
        ).thenAnswer((_) async => Tip());

        // Act
        state = ItemDefinitionStateImplementation(
          itemRepository: mockItemRepository,
          tipRepository: mockTipRepository,
          localeRepository: mockLocaleRepository,
          validators: mockValidators,
        );
        await Future.delayed(Duration.zero); // Allow async initialization

        // Assert
        expect(state.itemList, hasLength(1));
        expect(state.itemList.first.id, 'new-item-id');
        verify(() => mockItemRepository.createItem(any())).called(1);
      });

      test('loads existing items from storage', () async {
        // Arrange
        final existingItems = [
          Item(id: '1', name: 'Item 1'),
          Item(id: '2', name: 'Item 2'),
        ];
        when(
          () => mockItemRepository.getItemList(),
        ).thenAnswer((_) async => existingItems);
        when(
          () => mockTipRepository.checkIfTipHasBeenInitialized(),
        ).thenAnswer((_) async => false);
        when(
          () => mockTipRepository.createTip(),
        ).thenAnswer((_) async => Tip());

        // Act
        state = ItemDefinitionStateImplementation(
          itemRepository: mockItemRepository,
          tipRepository: mockTipRepository,
          localeRepository: mockLocaleRepository,
          validators: mockValidators,
        );
        await Future.delayed(Duration.zero); // Allow async initialization

        // Assert
        expect(state.itemList, hasLength(2));
        expect(state.itemList[0].id, '1');
        expect(state.itemList[1].id, '2');
        verifyNever(() => mockItemRepository.createItem());
      });

      test('creates default tip when tip has not been initialized', () async {
        // Arrange
        when(
          () => mockItemRepository.getItemList(),
        ).thenAnswer((_) async => [Item(id: 'existing-item')]);
        when(
          () => mockTipRepository.checkIfTipHasBeenInitialized(),
        ).thenAnswer((_) async => false);
        when(
          () => mockTipRepository.createTip(any()),
        ).thenAnswer((_) async => Tip());

        // Act
        state = ItemDefinitionStateImplementation(
          itemRepository: mockItemRepository,
          tipRepository: mockTipRepository,
          localeRepository: mockLocaleRepository,
          validators: mockValidators,
        );
        await Future.delayed(Duration.zero); // Allow async initialization

        // Assert
        expect(state.tip, isNotNull);
        verify(() => mockTipRepository.createTip(any())).called(1);
      });

      test('loads existing tip from storage', () async {
        // Arrange
        final existingTip = Tip(percentage: 20, amount: 5.0);
        when(
          () => mockItemRepository.getItemList(),
        ).thenAnswer((_) async => [Item(id: 'existing-item')]);
        when(
          () => mockTipRepository.checkIfTipHasBeenInitialized(),
        ).thenAnswer((_) async => true);
        when(
          () => mockTipRepository.getTip(),
        ).thenAnswer((_) async => existingTip);

        // Act
        state = ItemDefinitionStateImplementation(
          itemRepository: mockItemRepository,
          tipRepository: mockTipRepository,
          localeRepository: mockLocaleRepository,
          validators: mockValidators,
        );
        await Future.delayed(Duration.zero); // Allow async initialization

        // Assert
        expect(state.tip, isNotNull);
        expect(state.tip!.percentage, 20);
        expect(state.tip!.amount, 5.0);
        verifyNever(() => mockTipRepository.createTip());
      });

      test('handles case when tip was deleted (null tip)', () async {
        // Arrange
        when(
          () => mockItemRepository.getItemList(),
        ).thenAnswer((_) async => [Item(id: 'existing-item')]);
        when(
          () => mockTipRepository.checkIfTipHasBeenInitialized(),
        ).thenAnswer((_) async => true);
        when(() => mockTipRepository.getTip()).thenAnswer((_) async => null);

        // Act
        state = ItemDefinitionStateImplementation(
          itemRepository: mockItemRepository,
          tipRepository: mockTipRepository,
          localeRepository: mockLocaleRepository,
          validators: mockValidators,
        );
        await Future.delayed(Duration.zero); // Allow async initialization

        // Assert
        expect(state.tip, isNull);
        verifyNever(() => mockTipRepository.createTip());
      });
    }); // End of Initialization group

    group('Validation', () {
      setUp(() {
        state = ItemDefinitionStateImplementation(
          itemRepository: mockItemRepository,
          tipRepository: mockTipRepository,
          localeRepository: mockLocaleRepository,
          validators: mockValidators,
        );
      });

      test('validateItemPrice delegates to Validators.validatePrice', () {
        when(() => mockValidators.validatePrice('12.34')).thenReturn(null);
        expect(state.validateItemPrice('12.34'), isNull);
        verify(() => mockValidators.validatePrice('12.34')).called(1);
      });

      test('validateItemPrice returns error from Validators', () {
        when(() => mockValidators.validatePrice('0')).thenReturn('Must be positive');
        expect(state.validateItemPrice('0'), 'Must be positive');
        verify(() => mockValidators.validatePrice('0')).called(1);
      });

      test('validateItemQuantity delegates to Validators.validateQuantity', () {
        when(() => mockValidators.validateQuantity('2')).thenReturn(null);
        expect(state.validateItemQuantity('2'), isNull);
        verify(() => mockValidators.validateQuantity('2')).called(1);
      });

      test('validateItemQuantity returns error from Validators', () {
        when(() => mockValidators.validateQuantity('0')).thenReturn('Must be positive');
        expect(state.validateItemQuantity('0'), 'Must be positive');
        verify(() => mockValidators.validateQuantity('0')).called(1);
      });

      test('validateItemTotal delegates to Validators.validatePrice', () {
        when(() => mockValidators.validatePrice('99.99')).thenReturn(null);
        expect(state.validateItemTotal('99.99'), isNull);
        verify(() => mockValidators.validatePrice('99.99')).called(1);
      });

      test('validateItemTotal returns error from Validators', () {
        when(() => mockValidators.validatePrice('0')).thenReturn('Must be positive');
        expect(state.validateItemTotal('0'), 'Must be positive');
        verify(() => mockValidators.validatePrice('0')).called(1);
      });

      test('validateTipPercentage delegates to Validators.validateQuantity', () {
        when(() => mockValidators.validateQuantity('15')).thenReturn(null);
        expect(state.validateTipPercentage('15'), isNull);
        verify(() => mockValidators.validateQuantity('15')).called(1);
      });

      test('validateTipPercentage returns error from Validators', () {
        when(() => mockValidators.validateQuantity('0')).thenReturn('Must be positive');
        expect(state.validateTipPercentage('0'), 'Must be positive');
        verify(() => mockValidators.validateQuantity('0')).called(1);
      });

      test('validateTipAmount delegates to Validators.validatePrice', () {
        when(() => mockValidators.validatePrice('5.00')).thenReturn(null);
        expect(state.validateTipAmount('5.00'), isNull);
        verify(() => mockValidators.validatePrice('5.00')).called(1);
      });

      test('validateTipAmount returns error from Validators', () {
        when(() => mockValidators.validatePrice('0')).thenReturn('Must be positive');
        expect(state.validateTipAmount('0'), 'Must be positive');
        verify(() => mockValidators.validatePrice('0')).called(1);
      });
    });

    group('updateItemPrice', () {
      test('updates item price and recalculates total', () async {
        // Arrange
        final existingItems = [
          Item(id: '1', name: 'Item 1', price: 10.0, quantity: 2, total: 20.0),
        ];
        when(
          () => mockItemRepository.getItemList(),
        ).thenAnswer((_) async => existingItems);
        when(() => mockItemRepository.updateItem(any())).thenAnswer(
          (_) async => [
            Item(
              id: '1',
              name: 'Item 1',
              price: 15.0,
              quantity: 2,
              total: 30.0,
            ),
          ],
        );
        when(
          () => mockTipRepository.checkIfTipHasBeenInitialized(),
        ).thenAnswer((_) async => false);
        when(
          () => mockTipRepository.createTip(),
        ).thenAnswer((_) async => Tip());
        when(() => mockTipRepository.updateTip(any())).thenAnswer((_) async {});

        state = ItemDefinitionStateImplementation(
          itemRepository: mockItemRepository,
          tipRepository: mockTipRepository,
          localeRepository: mockLocaleRepository,
          validators: mockValidators,
        );
        await Future.delayed(Duration.zero);

        // Act
        state.onItemPriceChanged('1', '15.0');

        // Assert
        expect(state.itemList.first.price, 15.0);
        expect(state.itemList.first.total, 30.0);
        verify(() => mockItemRepository.updateItem(any())).called(1);
      });

      test(
        'updates tip amount when item price changes and tip is present',
        () async {
          // Arrange
          final existingItems = [
            Item(
              id: '1',
              name: 'Item 1',
              price: 10.0,
              quantity: 2,
              total: 20.0,
            ),
          ];
          final existingTip = Tip(percentage: 15, amount: 3.0); // 15% of $20
          when(
            () => mockItemRepository.getItemList(),
          ).thenAnswer((_) async => existingItems);
          when(() => mockItemRepository.updateItem(any())).thenAnswer(
            (_) async => [
              Item(
                id: '1',
                name: 'Item 1',
                price: 15.0,
                quantity: 2,
                total: 30.0,
              ),
            ],
          );
          when(
            () => mockTipRepository.checkIfTipHasBeenInitialized(),
          ).thenAnswer((_) async => true);
          when(
            () => mockTipRepository.getTip(),
          ).thenAnswer((_) async => existingTip);
          when(
            () => mockTipRepository.updateTip(any()),
          ).thenAnswer((_) async {});

          state = ItemDefinitionStateImplementation(
            itemRepository: mockItemRepository,
            tipRepository: mockTipRepository,
            localeRepository: mockLocaleRepository,
            validators: mockValidators,
          );
          await Future.delayed(Duration.zero);

          // Act
          state.onItemPriceChanged('1', '15.0');

          // Assert
          expect(state.itemList.first.total, 30.0);
          expect(state.tip!.percentage, 15); // Percentage stays the same
          expect(state.tip!.amount, 4.5); // 15% of new total $30
          verify(() => mockItemRepository.updateItem(any())).called(1);
          verify(() => mockTipRepository.updateTip(any())).called(1);
        },
      );
    });

    group('updateItemName', () {
      test('updates item name', () async {
        // Arrange
        final existingItems = [
          Item(
            id: '1',
            name: 'Old Name',
            price: 10.0,
            quantity: 1,
            total: 10.0,
          ),
        ];
        when(
          () => mockItemRepository.getItemList(),
        ).thenAnswer((_) async => existingItems);
        when(() => mockItemRepository.updateItem(any())).thenAnswer(
          (_) async => [
            Item(
              id: '1',
              name: 'New Name',
              price: 10.0,
              quantity: 1,
              total: 10.0,
            ),
          ],
        );
        when(
          () => mockTipRepository.checkIfTipHasBeenInitialized(),
        ).thenAnswer((_) async => false);
        when(
          () => mockTipRepository.createTip(),
        ).thenAnswer((_) async => Tip());
        when(() => mockTipRepository.updateTip(any())).thenAnswer((_) async {});

        state = ItemDefinitionStateImplementation(
          itemRepository: mockItemRepository,
          tipRepository: mockTipRepository,
          localeRepository: mockLocaleRepository,
          validators: mockValidators,
        );
        await Future.delayed(Duration.zero);

        // Act
        state.onItemNameChanged('1', 'New Name');

        // Assert
        expect(state.itemList.first.name, 'New Name');
        expect(state.itemList.first.price, 10.0); // Other fields unchanged
        expect(state.itemList.first.quantity, 1);
        expect(state.itemList.first.total, 10.0);
        verify(() => mockItemRepository.updateItem(any())).called(1);
      });

      test('does not update tip when item name changes', () async {
        // Arrange
        final existingItems = [
          Item(
            id: '1',
            name: 'Old Name',
            price: 10.0,
            quantity: 1,
            total: 10.0,
          ),
        ];
        final existingTip = Tip(percentage: 15, amount: 1.5);
        when(
          () => mockItemRepository.getItemList(),
        ).thenAnswer((_) async => existingItems);
        when(() => mockItemRepository.updateItem(any())).thenAnswer(
          (_) async => [
            Item(
              id: '1',
              name: 'New Name',
              price: 10.0,
              quantity: 1,
              total: 10.0,
            ),
          ],
        );
        when(
          () => mockTipRepository.checkIfTipHasBeenInitialized(),
        ).thenAnswer((_) async => true);
        when(
          () => mockTipRepository.getTip(),
        ).thenAnswer((_) async => existingTip);

        state = ItemDefinitionStateImplementation(
          itemRepository: mockItemRepository,
          tipRepository: mockTipRepository,
          localeRepository: mockLocaleRepository,
          validators: mockValidators,
        );
        await Future.delayed(Duration.zero);

        // Act
        state.onItemNameChanged('1', 'New Name');

        // Assert
        expect(state.itemList.first.name, 'New Name');
        expect(state.tip!.percentage, 15); // Tip unchanged
        expect(state.tip!.amount, 1.5); // Tip unchanged
        verify(() => mockItemRepository.updateItem(any())).called(1);
        verifyNever(
          () => mockTipRepository.updateTip(any()),
        ); // Tip should not be updated
      });
    });

    group('updateItemQuantity', () {
      test('updates item quantity and recalculates total', () async {
        // Arrange
        final existingItems = [
          Item(id: '1', name: 'Item 1', price: 10.0, quantity: 2, total: 20.0),
        ];
        when(
          () => mockItemRepository.getItemList(),
        ).thenAnswer((_) async => existingItems);
        when(() => mockItemRepository.updateItem(any())).thenAnswer(
          (_) async => [
            Item(
              id: '1',
              name: 'Item 1',
              price: 10.0,
              quantity: 3,
              total: 30.0,
            ),
          ],
        );
        when(
          () => mockTipRepository.checkIfTipHasBeenInitialized(),
        ).thenAnswer((_) async => false);
        when(
          () => mockTipRepository.createTip(),
        ).thenAnswer((_) async => Tip());
        when(() => mockTipRepository.updateTip(any())).thenAnswer((_) async {});

        state = ItemDefinitionStateImplementation(
          itemRepository: mockItemRepository,
          tipRepository: mockTipRepository,
          localeRepository: mockLocaleRepository,
          validators: mockValidators,
        );
        await Future.delayed(Duration.zero);

        // Act
        state.onItemQuantityChanged('1', '3');

        // Assert
        expect(state.itemList.first.quantity, 3);
        expect(state.itemList.first.total, 30.0);
        verify(() => mockItemRepository.updateItem(any())).called(1);
      });

      test(
        'updates tip amount when item quantity changes and tip is present',
        () async {
          // Arrange
          final existingItems = [
            Item(
              id: '1',
              name: 'Item 1',
              price: 10.0,
              quantity: 2,
              total: 20.0,
            ),
          ];
          final existingTip = Tip(percentage: 20, amount: 4.0); // 20% of $20
          when(
            () => mockItemRepository.getItemList(),
          ).thenAnswer((_) async => existingItems);
          when(() => mockItemRepository.updateItem(any())).thenAnswer(
            (_) async => [
              Item(
                id: '1',
                name: 'Item 1',
                price: 10.0,
                quantity: 3,
                total: 30.0,
              ),
            ],
          );
          when(
            () => mockTipRepository.checkIfTipHasBeenInitialized(),
          ).thenAnswer((_) async => true);
          when(
            () => mockTipRepository.getTip(),
          ).thenAnswer((_) async => existingTip);
          when(
            () => mockTipRepository.updateTip(any()),
          ).thenAnswer((_) async {});

          state = ItemDefinitionStateImplementation(
            itemRepository: mockItemRepository,
            tipRepository: mockTipRepository,
            localeRepository: mockLocaleRepository,
            validators: mockValidators,
          );
          await Future.delayed(Duration.zero);

          // Act
          state.onItemQuantityChanged('1', '3');

          // Assert
          expect(state.itemList.first.total, 30.0);
          expect(state.tip!.percentage, 20); // Percentage stays the same
          expect(state.tip!.amount, 6.0); // 20% of new total $30
          verify(() => mockItemRepository.updateItem(any())).called(1);
          verify(() => mockTipRepository.updateTip(any())).called(1);
        },
      );
    });

    group('updateItemTotal', () {
      test('updates item total and recalculates price', () async {
        // Arrange
        final existingItems = [
          Item(id: '1', name: 'Item 1', price: 10.0, quantity: 2, total: 20.0),
        ];
        when(
          () => mockItemRepository.getItemList(),
        ).thenAnswer((_) async => existingItems);
        when(() => mockItemRepository.updateItem(any())).thenAnswer(
          (_) async => [
            Item(
              id: '1',
              name: 'Item 1',
              price: 15.0,
              quantity: 2,
              total: 30.0,
            ),
          ],
        );
        when(
          () => mockTipRepository.checkIfTipHasBeenInitialized(),
        ).thenAnswer((_) async => false);
        when(
          () => mockTipRepository.createTip(),
        ).thenAnswer((_) async => Tip());
        when(() => mockTipRepository.updateTip(any())).thenAnswer((_) async {});

        state = ItemDefinitionStateImplementation(
          itemRepository: mockItemRepository,
          tipRepository: mockTipRepository,
          localeRepository: mockLocaleRepository,
          validators: mockValidators,
        );
        await Future.delayed(Duration.zero);

        // Act
        state.onItemTotalChanged('1', '30.0');

        // Assert
        expect(state.itemList.first.total, 30.0);
        expect(state.itemList.first.price, 15.0);
        verify(() => mockItemRepository.updateItem(any())).called(1);
      });

      test(
        'updates tip amount when item total changes and tip is present',
        () async {
          // Arrange
          final existingItems = [
            Item(
              id: '1',
              name: 'Item 1',
              price: 10.0,
              quantity: 2,
              total: 20.0,
            ),
          ];
          final existingTip = Tip(percentage: 10, amount: 2.0); // 10% of $20
          when(
            () => mockItemRepository.getItemList(),
          ).thenAnswer((_) async => existingItems);
          when(() => mockItemRepository.updateItem(any())).thenAnswer(
            (_) async => [
              Item(
                id: '1',
                name: 'Item 1',
                price: 15.0,
                quantity: 2,
                total: 30.0,
              ),
            ],
          );
          when(
            () => mockTipRepository.checkIfTipHasBeenInitialized(),
          ).thenAnswer((_) async => true);
          when(
            () => mockTipRepository.getTip(),
          ).thenAnswer((_) async => existingTip);
          when(
            () => mockTipRepository.updateTip(any()),
          ).thenAnswer((_) async {});

          state = ItemDefinitionStateImplementation(
            itemRepository: mockItemRepository,
            tipRepository: mockTipRepository,
            localeRepository: mockLocaleRepository,
            validators: mockValidators,
          );
          await Future.delayed(Duration.zero);

          // Act
          state.onItemTotalChanged('1', '30.0');

          // Assert
          expect(state.itemList.first.total, 30.0);
          expect(state.tip!.percentage, 10); // Percentage stays the same
          expect(state.tip!.amount, 3.0); // 10% of new total $30
          verify(() => mockItemRepository.updateItem(any())).called(1);
          verify(() => mockTipRepository.updateTip(any())).called(1);
        },
      );
    });

    group('addItem', () {
      test('adds new item to the list', () async {
        // Arrange
        final existingItems = [Item(id: '1', name: 'Item 1')];
        when(
          () => mockItemRepository.getItemList(),
        ).thenAnswer((_) async => existingItems);
        when(
          () => mockItemRepository.generateItem(),
        ).thenReturn(Item(id: '2', name: 'Item 2'));
        when(
          () => mockTipRepository.checkIfTipHasBeenInitialized(),
        ).thenAnswer((_) async => false);
        when(
          () => mockTipRepository.createTip(),
        ).thenAnswer((_) async => Tip());
        when(() => mockTipRepository.updateTip(any())).thenAnswer((_) async {});

        state = ItemDefinitionStateImplementation(
          itemRepository: mockItemRepository,
          tipRepository: mockTipRepository,
          localeRepository: mockLocaleRepository,
          validators: mockValidators,
        );
        await Future.delayed(Duration.zero);

        // Act
        state.addItem();

        // Assert
        expect(state.itemList, hasLength(2));
        expect(state.itemList[1].id, '2');
        verify(() => mockItemRepository.createItem(any())).called(
          1,
        ); // Only called during addItem (not during init since list wasn't empty)
      });

      test(
        'updates tip amount when new item is added and tip is present',
        () async {
          // Arrange
          final existingItems = [
            Item(
              id: '1',
              name: 'Item 1',
              price: 10.0,
              quantity: 1,
              total: 10.0,
            ),
          ];
          final existingTip = Tip(percentage: 15, amount: 1.5); // 15% of $10
          when(
            () => mockItemRepository.getItemList(),
          ).thenAnswer((_) async => existingItems);
          when(() => mockItemRepository.generateItem()).thenReturn(
            Item(id: '2', name: 'Item 2', price: 5.0, quantity: 1, total: 5.0),
          );
          when(
            () => mockTipRepository.checkIfTipHasBeenInitialized(),
          ).thenAnswer((_) async => true);
          when(
            () => mockTipRepository.getTip(),
          ).thenAnswer((_) async => existingTip);
          when(
            () => mockTipRepository.updateTip(any()),
          ).thenAnswer((_) async {});

          state = ItemDefinitionStateImplementation(
            itemRepository: mockItemRepository,
            tipRepository: mockTipRepository,
            localeRepository: mockLocaleRepository,
            validators: mockValidators,
          );
          await Future.delayed(Duration.zero);

          // Act
          state.addItem();

          // Assert
          expect(state.itemList, hasLength(2));
          expect(state.tip!.percentage, 15); // Percentage stays the same
          expect(state.tip!.amount, 2.25); // 15% of new total $15 ($10 + $5)
          verify(() => mockItemRepository.createItem(any())).called(1);
          verify(() => mockTipRepository.updateTip(any())).called(1);
        },
      );
    });

    group('removeItem', () {
      test('removes item from the list', () async {
        // Arrange
        final existingItems = [
          Item(id: '1', name: 'Item 1'),
          Item(id: '2', name: 'Item 2'),
        ];
        final updatedItems = [Item(id: '1', name: 'Item 1')];
        when(
          () => mockItemRepository.getItemList(),
        ).thenAnswer((_) async => existingItems);
        when(
          () => mockItemRepository.deleteItem('2'),
        ).thenAnswer((_) async => updatedItems);
        when(
          () => mockTipRepository.checkIfTipHasBeenInitialized(),
        ).thenAnswer((_) async => false);
        when(
          () => mockTipRepository.createTip(),
        ).thenAnswer((_) async => Tip());
        when(() => mockTipRepository.updateTip(any())).thenAnswer((_) async {});

        state = ItemDefinitionStateImplementation(
          itemRepository: mockItemRepository,
          tipRepository: mockTipRepository,
          localeRepository: mockLocaleRepository,
          validators: mockValidators,
        );
        await Future.delayed(Duration.zero);

        // Act
        state.removeItem('2');

        // Assert
        expect(state.itemList, hasLength(1));
        expect(state.itemList.first.id, '1');
        verify(() => mockItemRepository.deleteItem('2')).called(1);
      });

      test(
        'updates tip amount when item is removed and tip is present',
        () async {
          // Arrange
          final existingItems = [
            Item(
              id: '1',
              name: 'Item 1',
              price: 10.0,
              quantity: 1,
              total: 10.0,
            ),
            Item(id: '2', name: 'Item 2', price: 5.0, quantity: 1, total: 5.0),
          ];
          final updatedItems = [
            Item(
              id: '1',
              name: 'Item 1',
              price: 10.0,
              quantity: 1,
              total: 10.0,
            ),
          ];
          final existingTip = Tip(percentage: 20, amount: 3.0); // 20% of $15
          when(
            () => mockItemRepository.getItemList(),
          ).thenAnswer((_) async => existingItems);
          when(
            () => mockItemRepository.deleteItem('2'),
          ).thenAnswer((_) async => updatedItems);
          when(
            () => mockTipRepository.checkIfTipHasBeenInitialized(),
          ).thenAnswer((_) async => true);
          when(
            () => mockTipRepository.getTip(),
          ).thenAnswer((_) async => existingTip);
          when(
            () => mockTipRepository.updateTip(any()),
          ).thenAnswer((_) async {});

          state = ItemDefinitionStateImplementation(
            itemRepository: mockItemRepository,
            tipRepository: mockTipRepository,
            localeRepository: mockLocaleRepository,
            validators: mockValidators,
          );
          await Future.delayed(Duration.zero);

          // Act
          state.removeItem('2');

          // Assert
          expect(state.itemList, hasLength(1));
          expect(state.tip!.percentage, 20); // Percentage stays the same
          expect(state.tip!.amount, 2.0); // 20% of new total $10
          verify(() => mockItemRepository.deleteItem('2')).called(1);
          verify(() => mockTipRepository.updateTip(any())).called(1);
        },
      );

      test('throws StateError when trying to remove the only item', () async {
        // Arrange
        final existingItems = [Item(id: '1', name: 'Only Item')];
        when(
          () => mockItemRepository.getItemList(),
        ).thenAnswer((_) async => existingItems);
        when(
          () => mockTipRepository.checkIfTipHasBeenInitialized(),
        ).thenAnswer((_) async => false);
        when(
          () => mockTipRepository.createTip(),
        ).thenAnswer((_) async => Tip());

        state = ItemDefinitionStateImplementation(
          itemRepository: mockItemRepository,
          tipRepository: mockTipRepository,
          localeRepository: mockLocaleRepository,
          validators: mockValidators,
        );
        await Future.delayed(Duration.zero);

        // Act & Assert
        expect(
          () => state.removeItem('1'),
          throwsA(
            isA<StateError>().having(
              (e) => e.message,
              'message',
              state.strings.itemDeleteForbiddenError,
            ),
          ),
        );
        expect(state.itemList, hasLength(1));
      });

      test(
        'throws ArgumentError when trying to remove non-existent item',
        () async {
          // Arrange
          final existingItems = [
            Item(id: '1', name: 'Item 1'),
            Item(id: '2', name: 'Item 2'),
          ];
          when(
            () => mockItemRepository.getItemList(),
          ).thenAnswer((_) async => existingItems);
          when(
            () => mockTipRepository.checkIfTipHasBeenInitialized(),
          ).thenAnswer((_) async => false);
          when(
            () => mockTipRepository.createTip(),
          ).thenAnswer((_) async => Tip());

          state = ItemDefinitionStateImplementation(
            itemRepository: mockItemRepository,
            tipRepository: mockTipRepository,
            localeRepository: mockLocaleRepository,
            validators: mockValidators,
          );
          await Future.delayed(Duration.zero);

          // Act & Assert
          expect(
            () => state.removeItem('non-existent'),
            throwsA(
              isA<ArgumentError>().having(
                (e) => e.message,
                'message',
                state.strings.itemIdNotFoundError('non-existent'),
              ),
            ),
          );
        },
      );
    });

    group('addTip', () {
      test('adds new tip', () async {
        // Arrange
        when(
          () => mockItemRepository.getItemList(),
        ).thenAnswer((_) async => [Item(id: '1')]);
        when(
          () => mockTipRepository.checkIfTipHasBeenInitialized(),
        ).thenAnswer((_) async => true);
        when(
          () => mockTipRepository.getTip(),
        ).thenAnswer((_) async => null); // Tip was deleted
        when(
          () => mockTipRepository.createTip(),
        ).thenAnswer((_) async => Tip());

        state = ItemDefinitionStateImplementation(
          itemRepository: mockItemRepository,
          tipRepository: mockTipRepository,
          localeRepository: mockLocaleRepository,
          validators: mockValidators,
        );
        await Future.delayed(Duration.zero);

        // Act
        state.addTip();

        // Assert
        expect(state.tip, isNotNull);
        verify(() => mockTipRepository.createTip(any())).called(1);
      });
    });

    group('updateTipPercentage', () {
      test('updates tip percentage and recalculates amount', () async {
        // Arrange
        final existingItems = [
          Item(id: '1', price: 10.0, quantity: 1, total: 10.0),
        ];
        final existingTip = Tip(percentage: 10, amount: 1.0);
        when(
          () => mockItemRepository.getItemList(),
        ).thenAnswer((_) async => existingItems);
        when(
          () => mockTipRepository.checkIfTipHasBeenInitialized(),
        ).thenAnswer((_) async => true);
        when(
          () => mockTipRepository.getTip(),
        ).thenAnswer((_) async => existingTip);
        when(() => mockTipRepository.updateTip(any())).thenAnswer((_) async {});

        state = ItemDefinitionStateImplementation(
          itemRepository: mockItemRepository,
          tipRepository: mockTipRepository,
          localeRepository: mockLocaleRepository,
          validators: mockValidators,
        );
        await Future.delayed(Duration.zero);

        // Act
        state.onTipPercentageChanged('20');

        // Assert
        expect(state.tip!.percentage, 20);
        expect(state.tip!.amount, 2.0); // 20% of $10
        verify(() => mockTipRepository.updateTip(any())).called(1);
      });
    });

    group('updateTipAmount', () {
      test('updates tip amount and recalculates percentage', () async {
        // Arrange
        final existingItems = [
          Item(id: '1', price: 10.0, quantity: 1, total: 10.0),
        ];
        final existingTip = Tip(percentage: 10, amount: 1.0);
        when(
          () => mockItemRepository.getItemList(),
        ).thenAnswer((_) async => existingItems);
        when(
          () => mockTipRepository.checkIfTipHasBeenInitialized(),
        ).thenAnswer((_) async => true);
        when(
          () => mockTipRepository.getTip(),
        ).thenAnswer((_) async => existingTip);
        when(() => mockTipRepository.updateTip(any())).thenAnswer((_) async {});

        state = ItemDefinitionStateImplementation(
          itemRepository: mockItemRepository,
          tipRepository: mockTipRepository,
          localeRepository: mockLocaleRepository,
          validators: mockValidators,
        );
        await Future.delayed(Duration.zero);

        // Act
        state.onTipAmountChanged('1.5');

        // Assert
        expect(state.tip!.amount, 1.5);
        expect(state.tip!.percentage, 15); // $1.5 is 15% of $10
        verify(() => mockTipRepository.updateTip(any())).called(1);
      });
    });

    group('removeTip', () {
      test('removes tip', () async {
        // Arrange
        final existingTip = Tip(percentage: 10, amount: 1.0);
        when(
          () => mockItemRepository.getItemList(),
        ).thenAnswer((_) async => [Item(id: '1')]);
        when(
          () => mockTipRepository.checkIfTipHasBeenInitialized(),
        ).thenAnswer((_) async => true);
        when(
          () => mockTipRepository.getTip(),
        ).thenAnswer((_) async => existingTip);
        when(() => mockTipRepository.deleteTip()).thenAnswer((_) async {});

        state = ItemDefinitionStateImplementation(
          itemRepository: mockItemRepository,
          tipRepository: mockTipRepository,
          localeRepository: mockLocaleRepository,
          validators: mockValidators,
        );
        await Future.delayed(Duration.zero);

        // Act
        state.removeTip();

        // Assert
        expect(state.tip, isNull);
        verify(() => mockTipRepository.deleteTip()).called(1);
      });
    });

    group('Bill Total', () {
      test('calculates bill total correctly when no tip is present', () async {
        // Arrange
        final existingItems = [
          Item(id: '1', price: 10.0, quantity: 2, total: 20.0),
          Item(id: '2', price: 5.0, quantity: 1, total: 5.0),
        ];
        when(
          () => mockItemRepository.getItemList(),
        ).thenAnswer((_) async => existingItems);
        when(
          () => mockTipRepository.checkIfTipHasBeenInitialized(),
        ).thenAnswer((_) async => false);
        when(
          () => mockTipRepository.createTip(),
        ).thenAnswer((_) async => Tip());
        when(() => mockTipRepository.deleteTip()).thenAnswer((_) async {});

        state = ItemDefinitionStateImplementation(
          itemRepository: mockItemRepository,
          tipRepository: mockTipRepository,
          localeRepository: mockLocaleRepository,
          validators: mockValidators,
        );
        await Future.delayed(Duration.zero);

        // Remove the tip to test the no-tip scenario
        state.removeTip();

        // Act & Assert
        expect(state.billTotal, 25.0); // 20.0 + 5.0 + 0.0 (no tip)
      });

      test('calculates bill total correctly when tip is present', () async {
        // Arrange
        final existingItems = [
          Item(id: '1', price: 10.0, quantity: 2, total: 20.0),
          Item(id: '2', price: 5.0, quantity: 1, total: 5.0),
        ];
        final existingTip = Tip(percentage: 20, amount: 5.0);
        when(
          () => mockItemRepository.getItemList(),
        ).thenAnswer((_) async => existingItems);
        when(
          () => mockTipRepository.checkIfTipHasBeenInitialized(),
        ).thenAnswer((_) async => true);
        when(
          () => mockTipRepository.getTip(),
        ).thenAnswer((_) async => existingTip);

        state = ItemDefinitionStateImplementation(
          itemRepository: mockItemRepository,
          tipRepository: mockTipRepository,
          localeRepository: mockLocaleRepository,
          validators: mockValidators,
        );
        await Future.delayed(Duration.zero);

        // Act & Assert
        expect(state.billTotal, 30.0); // 20.0 + 5.0 + 5.0 (tip)
      });

      test('returns correct first line text when no tip is present', () async {
        // Arrange
        final existingItems = [
          Item(id: '1', price: 10.0, quantity: 1, total: 10.0),
        ];
        when(
          () => mockItemRepository.getItemList(),
        ).thenAnswer((_) async => existingItems);
        when(
          () => mockTipRepository.checkIfTipHasBeenInitialized(),
        ).thenAnswer((_) async => false);
        when(
          () => mockTipRepository.createTip(),
        ).thenAnswer((_) async => Tip());
        when(() => mockStrings.totalLabel).thenReturn('Total');

        state = ItemDefinitionStateImplementation(
          itemRepository: mockItemRepository,
          tipRepository: mockTipRepository,
          localeRepository: mockLocaleRepository,
          validators: mockValidators,
        );
        await Future.delayed(Duration.zero);

        // Remove the tip to test the no-tip scenario
        state.removeTip();

        // Act & Assert
        expect(state.billTotalFirstLine, 'Total');
      });

      test('returns correct first line text when tip is present', () async {
        // Arrange
        final existingItems = [
          Item(id: '1', price: 10.0, quantity: 1, total: 10.0),
          Item(id: '2', price: 5.0, quantity: 2, total: 10.0),
        ];
        final existingTip = Tip(percentage: 15, amount: 3.0);
        when(
          () => mockItemRepository.getItemList(),
        ).thenAnswer((_) async => existingItems);
        when(
          () => mockTipRepository.checkIfTipHasBeenInitialized(),
        ).thenAnswer((_) async => true);
        when(
          () => mockTipRepository.getTip(),
        ).thenAnswer((_) async => existingTip);

        state = ItemDefinitionStateImplementation(
          itemRepository: mockItemRepository,
          tipRepository: mockTipRepository,
          localeRepository: mockLocaleRepository,
          validators: mockValidators,
        );
        await Future.delayed(Duration.zero);

        // Act & Assert
        expect(state.billTotalFirstLine, '\$20.00 + \$3.00');
      });

      test('returns correct second line text', () async {
        // Arrange
        final existingItems = [
          Item(id: '1', price: 15.0, quantity: 1, total: 15.0),
        ];
        final existingTip = Tip(percentage: 20, amount: 3.0);
        when(
          () => mockItemRepository.getItemList(),
        ).thenAnswer((_) async => existingItems);
        when(
          () => mockTipRepository.checkIfTipHasBeenInitialized(),
        ).thenAnswer((_) async => true);
        when(
          () => mockTipRepository.getTip(),
        ).thenAnswer((_) async => existingTip);

        state = ItemDefinitionStateImplementation(
          itemRepository: mockItemRepository,
          tipRepository: mockTipRepository,
          localeRepository: mockLocaleRepository,
          validators: mockValidators,
        );
        await Future.delayed(Duration.zero);

        // Act & Assert
        expect(state.billTotalSecondLine, '\$18.00');
      });

      test('updates bill total when item changes', () async {
        // Arrange
        final existingItems = [
          Item(id: '1', price: 10.0, quantity: 1, total: 10.0),
        ];
        final existingTip = Tip(percentage: 10, amount: 1.0);
        when(
          () => mockItemRepository.getItemList(),
        ).thenAnswer((_) async => existingItems);
        when(
          () => mockTipRepository.checkIfTipHasBeenInitialized(),
        ).thenAnswer((_) async => true);
        when(
          () => mockTipRepository.getTip(),
        ).thenAnswer((_) async => existingTip);
        when(() => mockItemRepository.updateItem(any())).thenAnswer((_) async => []);
        when(() => mockTipRepository.updateTip(any())).thenAnswer((_) async {});

        state = ItemDefinitionStateImplementation(
          itemRepository: mockItemRepository,
          tipRepository: mockTipRepository,
          localeRepository: mockLocaleRepository,
          validators: mockValidators,
        );
        await Future.delayed(Duration.zero);

        final initialBillTotal = state.billTotal;
        expect(initialBillTotal, 11.0); // 10.0 + 1.0

        // Act
        state.onItemPriceChanged('1', '20.0');

        // Assert
        expect(state.billTotal, 22.0); // 20.0 + 2.0 (updated tip)
      });
    });
  });

  group('Continue to Next Step', () {
    test('should provide form key for form validation', () async {
      // Arrange
      state = ItemDefinitionStateImplementation(
        itemRepository: mockItemRepository,
        tipRepository: mockTipRepository,
        localeRepository: mockLocaleRepository,
        validators: mockValidators,
      );
      await Future.delayed(Duration.zero); // Allow async initialization

      // Assert
      expect(state.formKey, isA<GlobalKey<FormState>>());
    });

    test('should start with autovalidation disabled', () async {
      // Arrange
      state = ItemDefinitionStateImplementation(
        itemRepository: mockItemRepository,
        tipRepository: mockTipRepository,
        localeRepository: mockLocaleRepository,
        validators: mockValidators,
      );
      await Future.delayed(Duration.zero); // Allow async initialization

      // Assert
      expect(state.autovalidateMode, AutovalidateMode.disabled);
    });

    test('should enable autovalidation when continue button is pressed', () async {
      // Arrange
      state = ItemDefinitionStateImplementation(
        itemRepository: mockItemRepository,
        tipRepository: mockTipRepository,
        localeRepository: mockLocaleRepository,
        validators: mockValidators,
      );
      await Future.delayed(Duration.zero); // Allow async initialization

      // Initially autovalidation should be disabled
      expect(state.autovalidateMode, AutovalidateMode.disabled);

      // Act
      state.continueToNextStep();

      // Assert - Autovalidation should now be enabled
      expect(state.autovalidateMode, AutovalidateMode.always);
    });

    test('should validate form when continue button is pressed', () async {
      // Arrange
      state = ItemDefinitionStateImplementation(
        itemRepository: mockItemRepository,
        tipRepository: mockTipRepository,
        localeRepository: mockLocaleRepository,
        validators: mockValidators,
      );
      await Future.delayed(Duration.zero); // Allow async initialization

      // Act & Assert - This test verifies that the form validation logic is called
      // The actual validation will depend on the form being properly set up
      expect(() => state.continueToNextStep(), returnsNormally);
    });
  });
}
