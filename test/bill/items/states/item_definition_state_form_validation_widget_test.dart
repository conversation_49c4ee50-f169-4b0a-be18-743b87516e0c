import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:bill_splitter_2/bill/items/states/item_definition_state_implementation.dart';
import 'package:bill_splitter_2/bill/items/repositories/item_repository.dart';
import 'package:bill_splitter_2/bill/items/repositories/tip_repository.dart';
import 'package:bill_splitter_2/localization/repositories/locale_repository.dart';
import 'package:bill_splitter_2/localization/repositories/strings.dart';
import 'package:bill_splitter_2/utils/validators.dart';
import 'package:bill_splitter_2/bill/items/models/item.dart';
import 'package:bill_splitter_2/bill/items/models/tip.dart';
import 'package:bill_splitter_2/components/input/form_text_field.dart';

class MockItemRepository extends Mock implements ItemRepository {}
class MockTipRepository extends Mock implements TipRepository {}
class MockLocaleRepository extends Mock implements LocaleRepository {}
class MockValidators extends Mock implements Validators {}
class MockStrings extends Mock implements Strings {}

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  group('ItemDefinitionState Form Validation Widget Tests', () {
    late MockItemRepository mockItemRepository;
    late MockTipRepository mockTipRepository;
    late MockLocaleRepository mockLocaleRepository;
    late MockValidators mockValidators;
    late MockStrings mockStrings;
    late ItemDefinitionStateImplementation state;

    setUpAll(() {
      registerFallbackValue(Tip());
      registerFallbackValue(Item(id: 'itemId'));
    });

    setUp(() {
      mockItemRepository = MockItemRepository();
      mockTipRepository = MockTipRepository();
      mockLocaleRepository = MockLocaleRepository();
      mockValidators = MockValidators();
      mockStrings = MockStrings();

      // Setup basic mocks
      when(() => mockItemRepository.getItemList()).thenAnswer((_) async => []);
      when(() => mockItemRepository.generateItem()).thenReturn(Item(id: 'test-item-id'));
      when(() => mockItemRepository.createItem(any())).thenAnswer((_) async => []);
      when(() => mockTipRepository.checkIfTipHasBeenInitialized()).thenAnswer((_) async => false);
      when(() => mockTipRepository.createTip(any())).thenAnswer((_) async => Tip());
      when(() => mockLocaleRepository.strings).thenReturn(mockStrings);

      // Setup validation mocks to return error messages
      when(() => mockValidators.validatePrice(any())).thenReturn('Invalid price');
      when(() => mockValidators.validateQuantity(any())).thenReturn('Invalid quantity');

      state = ItemDefinitionStateImplementation(
        itemRepository: mockItemRepository,
        tipRepository: mockTipRepository,
        localeRepository: mockLocaleRepository,
        validators: mockValidators,
      );
    });

    testWidgets('should show validation errors when continue is pressed with invalid fields', (WidgetTester tester) async {
      // Arrange - Create a simple form with FormTextField widgets
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Form(
              key: state.formKey,
              child: Column(
                children: [
                  FormTextField.currency(
                    label: 'Price',
                    initialValue: '',
                    onChanged: (_) {},
                    validator: state.validateItemPrice,
                    autovalidateMode: state.autovalidateMode,
                  ),
                  FormTextField.integer(
                    label: 'Quantity',
                    initialValue: '',
                    onChanged: (_) {},
                    validator: state.validateItemQuantity,
                    autovalidateMode: state.autovalidateMode,
                  ),
                  ElevatedButton(
                    onPressed: state.continueToNextStep,
                    child: const Text('Continue'),
                  ),
                ],
              ),
            ),
          ),
        ),
      );

      // Initially, no validation errors should be visible
      expect(find.text('Invalid price'), findsNothing);
      expect(find.text('Invalid quantity'), findsNothing);

      // Act - Press the continue button
      await tester.tap(find.text('Continue'));
      await tester.pump(); // Trigger rebuild after state change

      // Assert - Validation errors should now be visible
      expect(find.text('Invalid price'), findsOneWidget);
      expect(find.text('Invalid quantity'), findsOneWidget);
    });

    testWidgets('should not show validation errors initially with autovalidate disabled', (WidgetTester tester) async {
      // Arrange - Create form with invalid initial values
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Form(
              key: state.formKey,
              child: Column(
                children: [
                  FormTextField.currency(
                    label: 'Price',
                    initialValue: 'invalid',
                    onChanged: (_) {},
                    validator: state.validateItemPrice,
                    autovalidateMode: state.autovalidateMode,
                  ),
                ],
              ),
            ),
          ),
        ),
      );

      // Assert - No validation errors should be visible initially
      expect(find.text('Invalid price'), findsNothing);
      expect(state.autovalidateMode, AutovalidateMode.disabled);
    });

    testWidgets('should show validation errors immediately after continue is pressed', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Form(
              key: state.formKey,
              child: Column(
                children: [
                  FormTextField.currency(
                    label: 'Price',
                    initialValue: 'invalid',
                    onChanged: (_) {},
                    validator: state.validateItemPrice,
                    autovalidateMode: state.autovalidateMode,
                  ),
                  ElevatedButton(
                    onPressed: state.continueToNextStep,
                    child: const Text('Continue'),
                  ),
                ],
              ),
            ),
          ),
        ),
      );

      // Act - Press continue button
      await tester.tap(find.text('Continue'));
      await tester.pump();

      // Assert - Validation error should be visible and autovalidation enabled
      expect(find.text('Invalid price'), findsOneWidget);
      expect(state.autovalidateMode, AutovalidateMode.always);

      // Act - Change the field value (should trigger immediate validation)
      await tester.enterText(find.byType(TextFormField), 'still invalid');
      await tester.pump();

      // Assert - Error should still be visible due to autovalidation
      expect(find.text('Invalid price'), findsOneWidget);
    });

    testWidgets('should validate all form fields when continue is pressed', (WidgetTester tester) async {
      // Arrange - Create form with multiple invalid fields
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Form(
              key: state.formKey,
              child: Column(
                children: [
                  FormTextField.currency(
                    label: 'Price',
                    initialValue: '',
                    onChanged: (_) {},
                    validator: state.validateItemPrice,
                    autovalidateMode: state.autovalidateMode,
                  ),
                  FormTextField.integer(
                    label: 'Quantity',
                    initialValue: '',
                    onChanged: (_) {},
                    validator: state.validateItemQuantity,
                    autovalidateMode: state.autovalidateMode,
                  ),
                  FormTextField.currency(
                    label: 'Total',
                    initialValue: '',
                    onChanged: (_) {},
                    validator: state.validateItemTotal,
                    autovalidateMode: state.autovalidateMode,
                  ),
                  ElevatedButton(
                    onPressed: state.continueToNextStep,
                    child: const Text('Continue'),
                  ),
                ],
              ),
            ),
          ),
        ),
      );

      // Act - Press continue button
      await tester.tap(find.text('Continue'));
      await tester.pump();

      // Assert - All validation errors should be visible
      expect(find.text('Invalid price'), findsAtLeastNWidgets(1));
      expect(find.text('Invalid quantity'), findsOneWidget);

      // Verify that the form validation was called and failed
      expect(state.autovalidateMode, AutovalidateMode.always);
    });
  });
}
