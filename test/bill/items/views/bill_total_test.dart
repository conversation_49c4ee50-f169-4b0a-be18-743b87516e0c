import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:bill_splitter_2/bill/items/views/bill_total.dart';

void main() {
  group('BillTotal', () {
    testWidgets('displays first line and second line text', (tester) async {
      // Arrange
      const firstLine = 'Total';
      const secondLine = '\$25.00';

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: BillTotal(
              firstLine: firstLine,
              secondLine: secondLine,
            ),
          ),
        ),
      );

      // Assert
      expect(find.text(firstLine), findsOneWidget);
      expect(find.text(secondLine), findsOneWidget);
    });

    testWidgets('displays tip breakdown format correctly', (tester) async {
      // Arrange
      const firstLine = '\$20.00 + \$3.00';
      const secondLine = '\$23.00';

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: BillTotal(
              firstLine: firstLine,
              secondLine: secondLine,
            ),
          ),
        ),
      );

      // Assert
      expect(find.text(firstLine), findsOneWidget);
      expect(find.text(secondLine), findsOneWidget);
    });

    testWidgets('applies correct text styles', (tester) async {
      // Arrange
      const firstLine = 'Total';
      const secondLine = '\$25.00';

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: BillTotal(
              firstLine: firstLine,
              secondLine: secondLine,
            ),
          ),
        ),
      );

      // Assert
      final firstLineWidget = tester.widget<Text>(find.text(firstLine));
      final secondLineWidget = tester.widget<Text>(find.text(secondLine));

      // First line uses theme default (bodyMedium)
      expect(firstLineWidget.style, isNotNull);
      expect(secondLineWidget.style?.fontWeight, FontWeight.bold);
    });

    testWidgets('arranges text in column with center alignment', (tester) async {
      // Arrange
      const firstLine = 'Total';
      const secondLine = '\$25.00';

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: BillTotal(
              firstLine: firstLine,
              secondLine: secondLine,
            ),
          ),
        ),
      );

      // Assert
      final column = tester.widget<Column>(find.byType(Column));
      expect(column.mainAxisSize, MainAxisSize.min);
      expect(column.crossAxisAlignment, CrossAxisAlignment.center);
      expect(column.children, hasLength(2));
    });
  });
}
