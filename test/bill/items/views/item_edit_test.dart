import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../../lib/bill/items/views/item_edit.dart';
import '../../../../lib/components/button/delete_button.dart';

void main() {
  group('ItemEdit', () {
    testWidgets('shows delete button when showDeleteButton is true', (tester) async {
      // Arrange
      bool deletePressed = false;

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ItemEdit(
              nameLabel: 'Name',
              name: 'Test Item',
              onNameChanged: (_) {},
              priceLabel: 'Price',
              priceTextValue: '10.00',
              onPriceChanged: (_) {},
              quantityLabel: 'Quantity',
              quantityTextValue: '1',
              onQuantityChanged: (_) {},
              totalLabel: 'Total',
              totalTextValue: '10.00',
              onTotalChanged: (_) {},
              orderNumber: 1,
              onDeletePressed: () => deletePressed = true,
              showDeleteButton: true,
            ),
          ),
        ),
      );

      // Assert
      expect(find.byType(DeleteButton), findsOneWidget);
      
      // Test that delete button works
      await tester.tap(find.byType(DeleteButton));
      expect(deletePressed, isTrue);
    });

    testWidgets('hides delete button when showDeleteButton is false', (tester) async {
      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ItemEdit(
              nameLabel: 'Name',
              name: 'Test Item',
              onNameChanged: (_) {},
              priceLabel: 'Price',
              priceTextValue: '10.00',
              onPriceChanged: (_) {},
              quantityLabel: 'Quantity',
              quantityTextValue: '1',
              onQuantityChanged: (_) {},
              totalLabel: 'Total',
              totalTextValue: '10.00',
              onTotalChanged: (_) {},
              orderNumber: 1,
              onDeletePressed: () {},
              showDeleteButton: false,
            ),
          ),
        ),
      );

      // Assert
      expect(find.byType(DeleteButton), findsNothing);
    });

    testWidgets('shows delete button by default when showDeleteButton is not specified', (tester) async {
      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ItemEdit(
              nameLabel: 'Name',
              name: 'Test Item',
              onNameChanged: (_) {},
              priceLabel: 'Price',
              priceTextValue: '10.00',
              onPriceChanged: (_) {},
              quantityLabel: 'Quantity',
              quantityTextValue: '1',
              onQuantityChanged: (_) {},
              totalLabel: 'Total',
              totalTextValue: '10.00',
              onTotalChanged: (_) {},
              orderNumber: 1,
              onDeletePressed: () {},
              // showDeleteButton not specified, should default to true
            ),
          ),
        ),
      );

      // Assert
      expect(find.byType(DeleteButton), findsOneWidget);
    });
  });
}
