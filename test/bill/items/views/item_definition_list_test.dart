import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:bill_splitter_2/bill/items/models/item.dart';
import 'package:bill_splitter_2/bill/items/views/item_definition_list.dart';
import 'package:bill_splitter_2/bill/items/views/item_edit.dart';
import 'package:bill_splitter_2/components/button/delete_button.dart';

void main() {
  group('ItemDefinitionList', () {
    testWidgets('hides delete button when there is only one item', (tester) async {
      // Arrange
      final singleItem = [
        Item(id: '1', name: 'Item 1', price: 10.0, quantity: 1, total: 10.0),
      ];

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ItemDefinitionList(
              itemList: singleItem,
              nameLabel: 'Name',
              onNameChanged: (_, __) {},
              priceLabel: 'Price',
              onPriceChanged: (_, __) {},
              quantityLabel: 'Quantity',
              onQuantityChanged: (_, __) {},
              totalLabel: 'Total',
              onTotalChanged: (_, __) {},
              onDeleteItem: (_) {},
              autovalidateMode: AutovalidateMode.disabled,
            ),
          ),
        ),
      );

      // Assert
      expect(find.byType(DeleteButton), findsNothing);
    });

    testWidgets('shows delete button when there are multiple items', (tester) async {
      // Arrange
      final multipleItems = [
        Item(id: '1', name: 'Item 1', price: 10.0, quantity: 1, total: 10.0),
        Item(id: '2', name: 'Item 2', price: 5.0, quantity: 2, total: 10.0),
      ];

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ItemDefinitionList(
              itemList: multipleItems,
              nameLabel: 'Name',
              onNameChanged: (_, __) {},
              priceLabel: 'Price',
              onPriceChanged: (_, __) {},
              quantityLabel: 'Quantity',
              onQuantityChanged: (_, __) {},
              totalLabel: 'Total',
              onTotalChanged: (_, __) {},
              onDeleteItem: (_) {},
              autovalidateMode: AutovalidateMode.disabled,
            ),
          ),
        ),
      );

      // Assert
      expect(find.byType(DeleteButton), findsNWidgets(2)); // One for each item
    });

    testWidgets('passes correct showDeleteButton value to ItemEdit widgets', (tester) async {
      // Arrange
      final multipleItems = [
        Item(id: '1', name: 'Item 1', price: 10.0, quantity: 1, total: 10.0),
        Item(id: '2', name: 'Item 2', price: 5.0, quantity: 2, total: 10.0),
      ];

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ItemDefinitionList(
              itemList: multipleItems,
              nameLabel: 'Name',
              onNameChanged: (_, __) {},
              priceLabel: 'Price',
              onPriceChanged: (_, __) {},
              quantityLabel: 'Quantity',
              onQuantityChanged: (_, __) {},
              totalLabel: 'Total',
              onTotalChanged: (_, __) {},
              onDeleteItem: (_) {},
              autovalidateMode: AutovalidateMode.disabled,
            ),
          ),
        ),
      );

      // Assert
      final itemEditWidgets = tester.widgetList<ItemEdit>(find.byType(ItemEdit));
      expect(itemEditWidgets, hasLength(2));
      
      // Both items should have showDeleteButton = true since there are multiple items
      for (final itemEdit in itemEditWidgets) {
        expect(itemEdit.showDeleteButton, isTrue);
      }
    });
  });
}
