import 'package:bill_splitter_2/bill/items/models/tip.dart';
import 'package:bill_splitter_2/bill/items/repositories/tip_repository_implementation.dart';
import 'package:bill_splitter_2/local_storage/models/local_storage_key.dart';
import 'package:bill_splitter_2/local_storage/services/local_storage_service.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockLocalStorageService extends Mock implements LocalStorageService {}

void main() {
  late MockLocalStorageService mockLocalStorageService;
  late TipRepositoryImplementation tipRepository;

  setUpAll(() {
    registerFallbackValue(LocalStorageKey.tip);
  });

  setUp(() {
    mockLocalStorageService = MockLocalStorageService();
    tipRepository = TipRepositoryImplementation(
      localStorageService: mockLocalStorageService,
    );
  });

  group('TipRepositoryImplementation', () {
    group('checkIfTipHasBeenInitialized', () {
      test('returns true when tip key exists in storage', () async {
        // Arrange
        when(() => mockLocalStorageService.hasKey(LocalStorageKey.tip))
            .thenAnswer((_) async => true);

        // Act
        final result = await tipRepository.checkIfTipHasBeenInitialized();

        // Assert
        expect(result, isTrue);
        verify(() => mockLocalStorageService.hasKey(LocalStorageKey.tip))
            .called(1);
      });

      test('returns false when tip key does not exist in storage', () async {
        // Arrange
        when(() => mockLocalStorageService.hasKey(LocalStorageKey.tip))
            .thenAnswer((_) async => false);

        // Act
        final result = await tipRepository.checkIfTipHasBeenInitialized();

        // Assert
        expect(result, isFalse);
        verify(() => mockLocalStorageService.hasKey(LocalStorageKey.tip))
            .called(1);
      });
    });

    group('getTip', () {
      test('returns tip when valid tip data exists in storage', () async {
        // Arrange
        final tipJson = {'percentage': 15, 'amount': 12.5};
        when(() => mockLocalStorageService.getValue<Map<String, dynamic>>(
              LocalStorageKey.tip,
            )).thenAnswer((_) async => tipJson);

        // Act
        final result = await tipRepository.getTip();

        // Assert
        expect(result, isNotNull);
        expect(result!.percentage, equals(15));
        expect(result.amount, equals(12.5));
        verify(() => mockLocalStorageService.getValue<Map<String, dynamic>>(
              LocalStorageKey.tip,
            )).called(1);
      });

      test('returns null when no tip data exists in storage', () async {
        // Arrange
        when(() => mockLocalStorageService.getValue<Map<String, dynamic>>(
              LocalStorageKey.tip,
            )).thenAnswer((_) async => null);

        // Act
        final result = await tipRepository.getTip();

        // Assert
        expect(result, isNull);
        verify(() => mockLocalStorageService.getValue<Map<String, dynamic>>(
              LocalStorageKey.tip,
            )).called(1);
      });
    });

    group('createTip', () {
      test('creates and saves a new tip with default values', () async {
        // Arrange
        when(() => mockLocalStorageService.setValue<Map<String, dynamic>?>(
              LocalStorageKey.tip,
              any(),
            )).thenAnswer((_) => Future<void>.value());

        // Act
        final result = await tipRepository.createTip();

        // Assert
        expect(result, isNotNull);
        expect(result.percentage, equals(10)); // Default percentage
        expect(result.amount, equals(0)); // Default amount
        verify(() => mockLocalStorageService.setValue<Map<String, dynamic>?>(
              LocalStorageKey.tip,
              result.toJson(),
            )).called(1);
      });
    });

    group('updateTip', () {
      test('saves updated tip to storage', () async {
        // Arrange
        final tip = Tip(percentage: 20, amount: 15.0);
        when(() => mockLocalStorageService.setValue<Map<String, dynamic>?>(
              LocalStorageKey.tip,
              any(),
            )).thenAnswer((_) => Future<void>.value());

        // Act
        await tipRepository.updateTip(tip);

        // Assert
        verify(() => mockLocalStorageService.setValue<Map<String, dynamic>?>(
              LocalStorageKey.tip,
              tip.toJson(),
            )).called(1);
      });
    });

    group('deleteTip', () {
      test('removes tip from storage by saving null', () async {
        // Arrange
        when(() => mockLocalStorageService.setValue<Map<String, dynamic>?>(
              LocalStorageKey.tip,
              null,
            )).thenAnswer((_) => Future<void>.value());

        // Act
        await tipRepository.deleteTip();

        // Assert
        verify(() => mockLocalStorageService.setValue<Map<String, dynamic>?>(
              LocalStorageKey.tip,
              null,
            )).called(1);
      });
    });
  });
}
