# Item Definition Rules

## Business Rules (Design-Independent)

### Item Management
- **BR-001**: Item total calculation: total = price × quantity
- **BR-002**: When item price changes, total is recalculated automatically
- **BR-003**: When item quantity changes, total is recalculated automatically
- **BR-004**: When item total changes, price is recalculated: price = total ÷ quantity
- **BR-005**: Item order is assigned automatically based on position in list
- **BR-006**: When an item is deleted, remaining items' order numbers are updated sequentially

### Tip Management
- **BR-007**: Tip amount calculation: amount = sum of item totals × (percentage ÷ 100)
- **BR-008**: Tip percentage calculation: percentage = (amount ÷ sum of item totals) × 100
- **BR-009**: When item totals change, tip amount is recalculated if tip exists
- **BR-010**: Tip is automatically created by default with 10% and 0.00 amount
- **BR-011**: If tip has been previously initialized, load existing tip from storage

### Bill Total Calculation
- **BR-012**: Bill total calculation: bill total = sum of item totals + tip amount
- **BR-013**: Bill total is recalculated when any item total changes
- **BR-014**: Bill total is recalculated when tip amount changes

## User Interface Rules (Design-Focused)

### Initial State
- **UI-001**: Step displays list of items, tip section, bill total, add item button, and continue button
- **UI-002**: If stored bill exists, load stored bill data
- **UI-003**: If no stored bill exists, start with default bill containing one item
- **UI-004**: Default item has blank label, 0.00 price, quantity 1, and 0.00 total

### Item List Interactions
- **UI-005**: New items are added at the bottom of the list
- **UI-006**: Item order field is not editable by user
- **UI-007**: Item label accepts free text input
- **UI-008**: Item price accepts currency input
- **UI-009**: Item quantity accepts positive integer input
- **UI-010**: Item total accepts currency input
- **UI-011**: Delete button is visible only when more than one item exists
- **UI-012**: Add item button is always visible

### Tip Section Interactions
- **UI-013**: Tip section remains visually separate from item list
- **UI-014**: Tip has a fixed, non-editable label
- **UI-015**: Tip percentage accepts non-negative integer input
- **UI-016**: Tip amount accepts currency input
- **UI-017**: Delete tip button is visible when tip exists
- **UI-018**: Add tip button is visible when no tip exists

### Bill Total Display
- **UI-019**: Bill total section is always visible
- **UI-020**: Bill total displays exactly two lines of text
- **UI-021**: Bill total values cannot be directly edited
- **UI-022**: With tip: first line shows "$[item sum] + $[tip amount]", second line shows bill total
- **UI-023**: Without tip: first line shows "Total", second line shows bill total

### Navigation
- **UI-024**: Continue button is always visible
- **UI-025**: Continue button enables autovalidation and validates all fields when clicked
- **UI-026**: If validation errors exist, display errors and remain on current step
- **UI-027**: If all fields valid, proceed to next step

## Technical Rules (Implementation Constraints)

### Data Validation
- **TR-001**: Minimum one item must always exist in the list
- **TR-002**: Item quantity must be a positive integer (> 0)
- **TR-003**: Tip percentage must be a non-negative integer (≥ 0)
- **TR-004**: Currency values support exactly 2 decimal places
- **TR-005**: Item labels have no character limit restrictions
- **TR-006**: Price and total values must be non-negative

### System Behavior
- **TR-007**: All calculations are performed in real-time
- **TR-008**: Bill state is automatically saved to local device storage asynchronously
- **TR-009**: Storage operations are fire-and-forget (do not block UI or throw errors if failed)
- **TR-010**: Local storage is used for session persistence across app restarts
- **TR-011**: Field focus management follows logical tab order
- **TR-012**: Validation occurs on continue button press, not on field blur

### Error Handling
- **TR-013**: Invalid currency input displays appropriate error message
- **TR-014**: Invalid quantity input displays appropriate error message
- **TR-015**: Division by zero in calculations is handled gracefully
- **TR-016**: All error messages are user-friendly and actionable
- **TR-017**: Storage failures do not interrupt user workflow or display error messages

## Examples and Edge Cases

### Example 1: Basic Item Calculation
- Item: Pizza, Price: $15.00, Quantity: 2
- Expected Total: $30.00 (15.00 × 2)

### Example 2: Tip Calculation
- Item Total Sum: $50.00, Tip Percentage: 18%
- Expected Tip Amount: $9.00 (50.00 × 0.18)

### Example 3: Bill Total with Tip
- Item Total Sum: $50.00, Tip Amount: $9.00
- Expected Bill Total: $59.00 (50.00 + 9.00)
- Display: "$50.00 + $9.00" (line 1), "$59.00" (line 2)

### Edge Case 1: Single Item Deletion
- When only one item exists, delete button should not be available
- Attempting to delete the last item should be prevented

### Edge Case 2: Zero Division
- When quantity is 0 and user edits total, price calculation should handle gracefully
- When item total sum is 0 and user edits tip amount, percentage calculation should handle gracefully
