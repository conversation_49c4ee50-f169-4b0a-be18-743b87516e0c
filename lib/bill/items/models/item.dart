class Item {
  const Item({required this.id, this.name = '', this.price = 0, this.quantity = 1, this.total = 0});

  final String id;
  final String name;
  final double price;
  final int quantity;
  final double total;

  Map<String, dynamic> toJson() {
    return {'id': id, 'name': name, 'price': price, 'quantity': quantity, 'total': total};
  }

  factory Item.fromJson(Map<String, dynamic> json) {
    return Item(
      id: json['id'],
      name: json['name'],
      price: json['price'],
      quantity: json['quantity'],
      total: json['total'],
    );
  }

  Item copyWith({String? name, double? price, int? quantity, double? total}) {
    return Item(
      id: id,
      name: name ?? this.name,
      price: price ?? this.price,
      quantity: quantity ?? this.quantity,
      total: total ?? this.total,
    );
  }
}
