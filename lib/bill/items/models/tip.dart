class Tip {
  const Tip({this.percentage = 10, this.amount = 0});

  final int percentage;
  final double amount;

  Map<String, dynamic> toJson() {
    return {'percentage': percentage, 'amount': amount};
  }

  factory Tip.fromJson(Map<String, dynamic> json) {
    return Tip(percentage: json['percentage'], amount: json['amount']);
  }

  Tip copyWith({int? percentage, double? amount}) {
    return Tip(percentage: percentage ?? this.percentage, amount: amount ?? this.amount);
  }
}
