import 'package:flutter/material.dart';

import '../../../components/button/delete_button.dart';
import '../../../components/card/list_item_card.dart';
import '../../../components/info/list_item_card_header.dart';
import '../../../components/input/form_text_field.dart';

class ItemEdit extends StatelessWidget {
  const ItemEdit({
    super.key,
    required this.nameLabel,
    this.name = '',
    required this.onNameChanged,
    required this.priceLabel,
    required this.priceTextValue,
    required this.onPriceChanged,
    this.priceValidator,
    required this.quantityLabel,
    required this.quantityTextValue,
    required this.onQuantityChanged,
    this.quantityValidator,
    required this.totalLabel,
    required this.totalTextValue,
    required this.onTotalChanged,
    this.totalValidator,
    required this.orderNumber,
    required this.onDeletePressed,
    this.showDeleteButton = true,
    this.autovalidateMode = AutovalidateMode.disabled,
  });

  final String nameLabel;
  final String name;
  final void Function(String name) onNameChanged;
  final String priceLabel;
  final String priceTextValue;
  final void Function(String price) onPriceChanged;
  final String? Function(String?)? priceValidator;
  final String quantityLabel;
  final String quantityTextValue;
  final void Function(String quantity) onQuantityChanged;
  final String? Function(String?)? quantityValidator;
  final String totalLabel;
  final String totalTextValue;
  final void Function(String total) onTotalChanged;
  final String? Function(String?)? totalValidator;
  final int orderNumber;
  final void Function() onDeletePressed;
  final bool showDeleteButton;
  final AutovalidateMode autovalidateMode;

  @override
  Widget build(BuildContext context) {
    return ListItemCard(
      child: Column(
        children: [
          // Header
          ListItemCardHeader(
            orderNumber: orderNumber,
            nameField: FormTextField(
              label: nameLabel,
              initialValue: name,
              onChanged: onNameChanged,
            ),
            iconButton: showDeleteButton
                ? DeleteButton(onPressed: onDeletePressed)
                : null,
          ),

          // Body
          const SizedBox(height: 12),
          Row(
            children: [
              // Price
              Flexible(
                child: FormTextField.currency(
                  prefixIcon: Icons.attach_money,
                  label: priceLabel,
                  initialValue: priceTextValue,
                  onChanged: onPriceChanged,
                  validator: priceValidator,
                  autovalidateMode: autovalidateMode,
                ),
              ),

              // Quantity
              const SizedBox(width: 8),
              Flexible(
                child: FormTextField.integer(
                  label: quantityLabel,
                  initialValue: quantityTextValue,
                  onChanged: onQuantityChanged,
                  validator: quantityValidator,
                  autovalidateMode: autovalidateMode,
                ),
              ),

              // Total
              const SizedBox(width: 8),
              Flexible(
                child: FormTextField.currency(
                  prefixIcon: Icons.payments,
                  label: totalLabel,
                  initialValue: totalTextValue,
                  onChanged: onTotalChanged,
                  validator: totalValidator,
                  autovalidateMode: autovalidateMode,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
