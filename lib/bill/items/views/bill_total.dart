import 'package:flutter/material.dart';

class BillTotal extends StatelessWidget {
  const BillTotal({
    super.key,
    required this.firstLine,
    required this.secondLine,
  });

  final String firstLine;
  final String secondLine;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          firstLine,
          style: Theme.of(context).textTheme.bodyMedium,
        ),
        Text(
          secondLine,
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }
}
