import 'package:flutter/material.dart';

import '../models/item.dart';
import 'item_edit.dart';

class ItemDefinitionList extends StatelessWidget {
  const ItemDefinitionList({
    super.key,
    required this.itemList,
    required this.nameLabel,
    required this.onNameChanged,
    required this.priceLabel,
    required this.onPriceChanged,
    this.priceValidator,
    required this.quantityLabel,
    required this.onQuantityChanged,
    this.quantityValidator,
    required this.totalLabel,
    required this.onTotalChanged,
    this.totalValidator,
    required this.onDeleteItem,
    required this.autovalidateMode,
  });

  final List<Item> itemList;
  final String nameLabel;
  final void Function(String itemId, String name) onNameChanged;
  final String priceLabel;
  final void Function(String itemId, String price) onPriceChanged;
  final String? Function(String?)? priceValidator;
  final String quantityLabel;
  final void Function(String itemId, String quantity) onQuantityChanged;
  final String? Function(String?)? quantityValidator;
  final String totalLabel;
  final void Function(String itemId, String total) onTotalChanged;
  final String? Function(String?)? totalValidator;
  final void Function(String itemId) onDeleteItem;
  final AutovalidateMode autovalidateMode;

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      separatorBuilder: (context, index) => const SizedBox(height: 12),
      shrinkWrap: true,
      physics: NeverScrollableScrollPhysics(),
      itemCount: itemList.length,
      itemBuilder: (context, index) {
        final item = itemList[index];
        return ItemEdit(
          orderNumber: index + 1,
          onDeletePressed: () => onDeleteItem(item.id),
          nameLabel: nameLabel,
          name: item.name,
          onNameChanged: (value) => onNameChanged(item.id, value),
          priceLabel: priceLabel,
          priceTextValue: item.price.toStringAsFixed(2),
          onPriceChanged: (value) => onPriceChanged(item.id, value),
          priceValidator: priceValidator,
          quantityLabel: quantityLabel,
          quantityTextValue: item.quantity.toString(),
          onQuantityChanged: (value) => onQuantityChanged(item.id, value),
          quantityValidator: quantityValidator,
          totalLabel: totalLabel,
          totalTextValue: item.total.toStringAsFixed(2),
          onTotalChanged: (value) => onTotalChanged(item.id, value),
          totalValidator: totalValidator,
          showDeleteButton: itemList.length > 1,
          autovalidateMode: autovalidateMode,
        );
      },
    );
  }
}
