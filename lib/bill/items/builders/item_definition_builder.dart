import 'package:flutter/material.dart';

import '../../../components/button/button.dart';
import '../../../locator/repositories/locator.dart';
import '../states/item_definition_state.dart';
import '../views/bill_total.dart';
import '../views/item_definition_list.dart';
import '../views/item_definition_page.dart';
import '../views/tip_edit.dart';

class ItemDefinitionBuilder extends StatelessWidget {
  const ItemDefinitionBuilder({super.key});

  @override
  Widget build(BuildContext context) {
    final state = Locator.instance.get<ItemDefinitionState>();
    return ListenableBuilder(
      listenable: state as ChangeNotifier,
      builder: (context, child) {
        return ItemDefinitionPage(
          formKey: state.formKey,
          addItemButton: Button(
            label: state.strings.addItemLabel,
            onPressed: state.addItem,
          ),
          total: BillTotal(
            firstLine: state.billTotalFirstLine,
            secondLine: state.billTotalSecondLine,
          ),
          continueButton: Button(
            label: state.strings.continueLabel,
            onPressed: state.continueToNextStep,
          ),
          itemList: ItemDefinitionList(
            itemList: state.itemList,
            nameLabel: state.strings.nameLabel,
            priceLabel: state.strings.priceLabel,
            quantityLabel: state.strings.quantityLabel,
            totalLabel: state.strings.totalLabel,
            onNameChanged: (itemId, name) =>
                state.onItemNameChanged(itemId, name),
            onPriceChanged: (itemId, price) =>
                state.onItemPriceChanged(itemId, price),
            onQuantityChanged: (itemId, quantity) =>
                state.onItemQuantityChanged(itemId, quantity),
            onTotalChanged: (itemId, total) =>
                state.onItemTotalChanged(itemId, total),
            priceValidator: state.validateItemPrice,
            quantityValidator: state.validateItemQuantity,
            totalValidator: state.validateItemTotal,
            onDeleteItem: state.removeItem,
            autovalidateMode: state.autovalidateMode,
          ),
          tip: state.tip == null
              ? Button(
                  label: state.strings.addTipLabel,
                  onPressed: state.addTip,
                )
              : TipEdit(
                  tipLabel: state.strings.tipLabel,
                  percentageLabel: state.strings.percentageLabel,
                  amountLabel: state.strings.amountLabel,
                  percentageTextValue: state.tip!.percentage.toString(),
                  amountTextValue: state.tip!.amount.toStringAsFixed(2),
                  onPercentageChanged: state.onTipPercentageChanged,
                  onAmountChanged: state.onTipAmountChanged,
                  percentageValidator: state.validateTipPercentage,
                  amountValidator: state.validateTipAmount,
                  onDeletePressed: state.removeTip,
                  autovalidateMode: state.autovalidateMode,
                ),
        );
      },
    );
  }
}
