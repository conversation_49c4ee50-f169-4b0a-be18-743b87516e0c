import '../../../local_storage/models/local_storage_key.dart';
import '../../../local_storage/services/local_storage_service.dart';
import '../../../unique_identifier/services/unique_identifier_service.dart';
import '../models/item.dart';
import 'item_repository.dart';

class ItemRepositoryImplementation implements ItemRepository {
  ItemRepositoryImplementation({
    required LocalStorageService localStorageService,
    required UniqueIdentifierService uniqueIdentifierService,
  }) : _localStorageService = localStorageService,
       _uniqueIdentifierService = uniqueIdentifierService;

  final LocalStorageService _localStorageService;
  final UniqueIdentifierService _uniqueIdentifierService;

  @override
  Future<List<Item>> getItemList() async {
    final itemListJson = await _localStorageService.getValue<List<dynamic>>(
      LocalStorageKey.itemList,
    );
    if (itemListJson == null) return [];
    return itemListJson.map((itemJson) => Item.fromJson(itemJson)).toList();
  }

  Future<void> _saveItemList(List<Item> items) {
    return _localStorageService.setValue(
      LocalStorageKey.itemList,
      items.map((item) => item.toJson()).toList(),
    );
  }

  @override
  Item generateItem() {
    final newItemId = _uniqueIdentifierService.generate();
    return Item(id: newItemId);
  }

  @override
  Future<List<Item>> createItem([Item? item]) async {
    final itemList = await getItemList();
    final newItem = item ?? generateItem();
    itemList.add(newItem);
    await _saveItemList(itemList);
    return itemList;
  }

  @override
  Future<List<Item>> updateItem(Item item) async {
    final itemList = await getItemList();
    final itemIndex = itemList.indexWhere((index) => index.id == item.id);
    itemList[itemIndex] = item;
    await _saveItemList(itemList);
    return itemList;
  }

  @override
  Future<List<Item>> deleteItem(String itemId) async {
    final itemList = await getItemList();

    // Check if item exists
    final itemIndex = itemList.indexWhere((index) => index.id == itemId);
    if (itemIndex == -1) {
      throw ArgumentError('Item with id "$itemId" not found');
    }

    // Prevent deletion if this is the only item in the list
    if (itemList.length == 1) {
      throw StateError('Cannot delete the only item in the list');
    }

    itemList.removeAt(itemIndex);
    await _saveItemList(itemList);
    return itemList;
  }
}
