import '../../../local_storage/models/local_storage_key.dart';
import '../../../local_storage/services/local_storage_service.dart';
import '../models/tip.dart';
import 'tip_repository.dart';

class TipRepositoryImplementation implements TipRepository {
  TipRepositoryImplementation({
    required LocalStorageService localStorageService,
  }) : _localStorageService = localStorageService;

  final LocalStorageService _localStorageService;

  @override
  Future<bool> checkIfTipHasBeenInitialized() async {
    return await _localStorageService.hasKey(LocalStorageKey.tip);
  }

  @override
  Future<Tip?> getTip() async {
    final tipJson = await _localStorageService.getValue<Map<String, dynamic>>(
      LocalStorageKey.tip,
    );
    if (tipJson == null) return null;
    return Tip.fromJson(tipJson);
  }

  Future<void> _saveTip(Tip? tip) {
    return _localStorageService.setValue(LocalStorageKey.tip, tip?.toJson());
  }

  @override
  Future<Tip> createTip([Tip? tip]) async {
    final newTip = tip ?? Tip();
    await _saveTip(newTip);
    return newTip;
  }

  @override
  Future<void> updateTip(Tip tip) => _saveTip(tip);

  @override
  Future<void> deleteTip() => _saveTip(null);
}
