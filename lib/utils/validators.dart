import '../localization/repositories/locale_repository.dart';
import 'extensions.dart';

class Validators {
  Validators({required LocaleRepository localeRepository})
    : _localeRepository = localeRepository;

  final LocaleRepository _localeRepository;

  String? validatePrice(String? value) {
    final doubleValue = (value ?? '').currency(
      locale: _localeRepository.currentLocale,
    );
    if (doubleValue <= 0) {
      return _localeRepository.strings.nonPositiveNumberError;
    }
    return null;
  }

  String? validateQuantity(String? value) {
    final intValue = int.tryParse(value ?? '') ?? 0;
    if (intValue <= 0) {
      return _localeRepository.strings.nonPositiveNumberError;
    }
    return null;
  }
}
