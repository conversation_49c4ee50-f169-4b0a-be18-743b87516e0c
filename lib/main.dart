import 'package:flutter/material.dart';

import 'app/builders/app_builder.dart';
import 'app/states/app_state.dart';
import 'app/states/app_state_implementation.dart';
import 'bill/items/repositories/item_repository.dart';
import 'bill/items/repositories/item_repository_implementation.dart';
import 'bill/items/repositories/tip_repository.dart';
import 'bill/items/repositories/tip_repository_implementation.dart';
import 'bill/items/states/item_definition_state.dart';
import 'bill/items/states/item_definition_state_implementation.dart';
import 'local_storage/services/local_storage_service.dart';
import 'local_storage/services/local_storage_service_implementation.dart';
import 'localization/repositories/locale_repository.dart';
import 'localization/repositories/locale_repository_implementation.dart';
import 'locator/repositories/locator.dart';
import 'theme/repositories/theme_repository.dart';
import 'theme/repositories/theme_repository_implementation.dart';
import 'unique_identifier/services/unique_identifier_service.dart';
import 'unique_identifier/services/unique_identifier_service_implementation.dart';
import 'utils/validators.dart';

void main() {
  _initializeDependencies();
  runApp(const AppBuilder());
}

void _initializeDependencies() {
  Locator.instance.registerLazySingleton<LocalStorageService>(
    () => LocalStorageServiceImplementation(),
  );
  Locator.instance.registerLazySingleton<UniqueIdentifierService>(
    () => UniqueIdentifierServiceImplementation(),
  );
  Locator.instance.registerLazySingleton<ThemeRepository>(
    () => ThemeRepositoryImplementation(
      localStorageService: Locator.instance.get<LocalStorageService>(),
    ),
  );
  Locator.instance.registerLazySingleton<ItemRepository>(
    () => ItemRepositoryImplementation(
      localStorageService: Locator.instance.get<LocalStorageService>(),
      uniqueIdentifierService: Locator.instance.get<UniqueIdentifierService>(),
    ),
  );
  Locator.instance.registerLazySingleton<TipRepository>(
    () => TipRepositoryImplementation(
      localStorageService: Locator.instance.get<LocalStorageService>(),
    ),
  );
  Locator.instance.registerLazySingleton<LocaleRepository>(
    () => LocaleRepositoryImplementation(
      localStorageService: Locator.instance.get<LocalStorageService>(),
    ),
  );
  Locator.instance.registerLazySingleton<Validators>(
    () =>
        Validators(localeRepository: Locator.instance.get<LocaleRepository>()),
  );
  Locator.instance.registerLazySingleton<AppState>(
    () => AppStateImplementation(
      themeRepository: Locator.instance.get<ThemeRepository>(),
      localeRepository: Locator.instance.get<LocaleRepository>(),
    ),
  );
  Locator.instance.registerLazySingleton<ItemDefinitionState>(
    () => ItemDefinitionStateImplementation(
      itemRepository: Locator.instance.get<ItemRepository>(),
      tipRepository: Locator.instance.get<TipRepository>(),
      localeRepository: Locator.instance.get<LocaleRepository>(),
      validators: Locator.instance.get<Validators>(),
    ),
  );
}
