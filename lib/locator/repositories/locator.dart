import 'locator_repository.dart';

enum _RegistrationType { singleton, lazySingleton }

class _Registration<T extends Object> {
  _Registration.singleton(this.instance)
    : type = _RegistrationType.singleton,
      factory = null;

  _Registration.lazySingleton(this.factory)
    : type = _RegistrationType.lazySingleton,
      instance = null;

  final _RegistrationType type;
  T? instance;
  final T Function()? factory;
}

class Locator implements LocatorRepository {
  Locator._();

  static final Locator _instance = Locator._();

  /// Get the singleton instance
  static Locator get instance => _instance;

  final Map<Type, _Registration> _registrations = {};

  @override
  void registerSingleton<T extends Object>(T instance) {
    _registrations[T] = _Registration<T>.singleton(instance);
  }

  @override
  void registerLazySingleton<T extends Object>(T Function() factory) {
    _registrations[T] = _Registration<T>.lazySingleton(factory);
  }

  @override
  T get<T extends Object>() {
    final registration = _registrations[T] as _Registration<T>?;

    if (registration == null) {
      throw StateError('Type $T is not registered');
    }

    switch (registration.type) {
      case _RegistrationType.singleton:
        return registration.instance!;

      case _RegistrationType.lazySingleton:
        if (registration.instance == null) {
          registration.instance = registration.factory!();
        }
        return registration.instance!;
    }
  }

  @override
  bool isRegistered<T extends Object>() {
    return _registrations.containsKey(T);
  }

  /// Reset all registrations (useful for testing)
  void reset() {
    _registrations.clear();
  }
}
