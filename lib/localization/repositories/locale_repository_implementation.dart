import 'package:flutter/material.dart';

import '../../local_storage/models/local_storage_key.dart';
import '../../local_storage/services/local_storage_service.dart';
import 'locale_repository.dart';
import 'strings.dart';
import 'strings_en.dart';

class LocaleRepositoryImplementation extends ChangeNotifier
    implements LocaleRepository {
  LocaleRepositoryImplementation({
    required LocalStorageService localStorageService,
    Locale initialLocale = const Locale('en'),
  }) : _localStorageService = localStorageService,
       _currentLocale = initialLocale;

  final LocalStorageService _localStorageService;
  Locale _currentLocale;

  @override
  Locale get currentLocale => _currentLocale;

  @override
  Future<void> setLocale(Locale locale) async {
    _currentLocale = locale;
    final localeString = _localeToString(locale);
    await _localStorageService.setValue(LocalStorageKey.locale, localeString);
    notifyListeners();
  }

  @override
  Future<void> loadSavedLocale() async {
    final localeString = await _localStorageService.getValue<String>(
      LocalStorageKey.locale,
    );
    if (localeString != null) {
      _currentLocale = _stringToLocale(localeString);
    }
  }

  String _localeToString(Locale locale) {
    var localeString = locale.languageCode;
    if ((locale.countryCode ?? '').isNotEmpty) {
      localeString += '_${locale.countryCode}';
    }
    return localeString;
  }

  Locale _stringToLocale(String localeString) {
    final parts = localeString.split('_');
    return Locale(
      parts[0],
      parts.length > 1 && parts[1].isNotEmpty ? parts[1] : null,
    );
  }

  @override
  Strings get strings {
    switch (currentLocale.languageCode) {
      case 'en':
      default:
        return StringsEn();
    }
  }
}
