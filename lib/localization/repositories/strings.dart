abstract class Strings {
  String get nameLabel;
  String get priceLabel;
  String get quantityLabel;
  String get totalLabel;
  String get tipLabel;
  String get percentageLabel;
  String get amountLabel;
  String get nonPositiveNumberError;
  String get addItemLabel;
  String get addTipLabel;
  String get continueLabel;
  String get itemDeleteForbiddenError;
  String itemIdNotFoundError(String itemId);
}
