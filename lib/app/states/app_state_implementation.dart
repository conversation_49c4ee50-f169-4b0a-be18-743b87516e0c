import 'package:flutter/material.dart';

import '../../localization/repositories/locale_repository.dart';
import '../../theme/repositories/theme_repository.dart';
import 'app_state.dart';

class AppStateImplementation extends ChangeNotifier implements AppState {
  final ThemeRepository _themeRepository;
  final LocaleRepository _localeRepository;

  AppStateImplementation({
    required ThemeRepository themeRepository,
    required LocaleRepository localeRepository,
  }) : _themeRepository = themeRepository,
       _localeRepository = localeRepository {
    _loadThemeMode();
    _loadLocale();
  }

  @override
  ThemeMode get themeMode => _themeMode;
  ThemeMode _themeMode = ThemeMode.light;

  @override
  ThemeData get lightTheme => _themeRepository.lightTheme;

  @override
  ThemeData get darkTheme => _themeRepository.darkTheme;

  @override
  Locale get locale => _localeRepository.currentLocale;

  Future<void> _loadThemeMode() async {
    _themeMode = await _themeRepository.loadThemeMode();
    notifyListeners();
  }

  Future<void> _loadLocale() async {
    await _localeRepository.loadSavedLocale();
    notifyListeners();
  }
}
