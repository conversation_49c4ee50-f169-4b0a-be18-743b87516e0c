import 'package:flutter/material.dart';

import '../../locator/repositories/locator.dart';
import '../../navigation/builders/navigation_builder.dart';
import '../states/app_state.dart';

class AppBuilder extends StatelessWidget {
  const AppBuilder({super.key});

  @override
  Widget build(BuildContext context) {
    final state = Locator.instance.get<AppState>();
    return ListenableBuilder(
      listenable: state as ChangeNotifier,
      builder: (context, child) {
        return MaterialApp.router(
          routerConfig: router,
          theme: state.lightTheme,
          darkTheme: state.darkTheme,
          themeMode: state.themeMode,
          debugShowCheckedModeBanner: false,
        );
      },
    );
  }
}
