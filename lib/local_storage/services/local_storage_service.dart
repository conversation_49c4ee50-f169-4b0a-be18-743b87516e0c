import '../models/local_storage_key.dart';

abstract class LocalStorageService {
  /// Store a value with the given key
  Future<void> setValue<T>(LocalStorageKey key, T value);

  /// Retrieve a value by key
  Future<T?> getValue<T>(LocalStorageKey key);

  /// Check if a key exists in storage
  Future<bool> hasKey(LocalStorageKey key);

  /// Remove a value by key
  Future<void> removeValue(LocalStorageKey key);

  /// Clear all stored values
  Future<void> clear();
}
