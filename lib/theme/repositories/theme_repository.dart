import 'package:flutter/material.dart';

/// Abstract repository for theme persistence
abstract class ThemeRepository {
  /// Save the current theme mode
  Future<void> saveThemeMode(ThemeMode themeMode);

  /// Load the saved theme mode
  Future<ThemeMode> loadThemeMode();

  /// Check if a theme mode has been saved
  Future<bool> hasThemeMode();

  ThemeData get lightTheme;
  ThemeData get darkTheme;
}
