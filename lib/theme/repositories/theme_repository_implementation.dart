import 'package:flutter/material.dart';

import '../../local_storage/models/local_storage_key.dart';
import '../../local_storage/services/local_storage_service.dart';
import 'theme_repository.dart';

/// Implementation of ThemeRepository using local storage
class ThemeRepositoryImplementation implements ThemeRepository {
  final LocalStorageService _localStorageService;

  const ThemeRepositoryImplementation({
    required LocalStorageService localStorageService,
  }) : _localStorageService = localStorageService;

  @override
  Future<void> saveThemeMode(ThemeMode themeMode) async {
    try {
      await _localStorageService.setValue(
        LocalStorageKey.theme,
        themeMode.name,
      );
    } catch (_) {
      // Silently fail - theme preference is not critical
      // The app will use the default theme if saving fails
    }
  }

  @override
  Future<ThemeMode> loadThemeMode() async {
    try {
      final themeModeString = await _localStorageService.getValue<String>(
        LocalStorageKey.theme,
      );
      if (themeModeString != null) {
        return ThemeMode.values.firstWhere(
          (mode) => mode.name == themeModeString,
          orElse: () => ThemeMode.system,
        );
      }
    } catch (_) {
      // Silently fail and return default
    }
    return ThemeMode.system;
  }

  @override
  Future<bool> hasThemeMode() async {
    try {
      return await _localStorageService.hasKey(LocalStorageKey.theme);
    } catch (_) {
      return false;
    }
  }

  @override
  ThemeData get lightTheme {
    const lightPrimaryColor = Color(0xFF6750A4);
    const lightSurfaceColor = Color(0xFFFEF7FF);
    const lightOnSurfaceColor = Color(0xFF1D1B20);
    const lightErrorColor = Color(0xFFB3261E);
    const lightOnErrorColor = Color(0xFFFFFFFF);
    const primaryTextColor = Color(0xFFFEF7FF);

    final colorScheme = ColorScheme.light(
      brightness: Brightness.light,
      primary: lightPrimaryColor,
      onPrimary: const Color(0xFFFFFFFF),
      primaryContainer: const Color(0xFFEADDFF),
      onPrimaryContainer: const Color(0xFF4F378B),
      secondary: const Color(0xFF625B71),
      onSecondary: const Color(0xFFFFFFFF),
      secondaryContainer: const Color(0xFFE8DEF8),
      onSecondaryContainer: const Color(0xFF4A4458),
      tertiary: const Color(0xFF7D5260),
      onTertiary: const Color(0xFFFFFFFF),
      tertiaryContainer: const Color(0xFFFFD8E4),
      onTertiaryContainer: const Color(0xFF633B48),
      error: lightErrorColor,
      onError: lightOnErrorColor,
      errorContainer: const Color(0xFFF9DEDC),
      onErrorContainer: const Color(0xFF8C1D18),
      surface: lightSurfaceColor,
      onSurface: lightOnSurfaceColor,
      onSurfaceVariant: const Color(0xFF49454F),
      outline: const Color(0xFF79747E),
      outlineVariant: const Color(0xFFCAC4D0),
      shadow: const Color(0xFF000000),
      scrim: const Color(0xFF000000),
      inverseSurface: const Color(0xFF322F35),
      onInverseSurface: const Color(0xFFF5EFF7),
      inversePrimary: const Color(0xFFD0BCFF),
      surfaceTint: lightPrimaryColor,
    );

    return ThemeData(
      useMaterial3: true,
      colorScheme: colorScheme,
      scaffoldBackgroundColor: lightSurfaceColor,
      canvasColor: lightSurfaceColor,
      cardColor: lightSurfaceColor,
      dividerColor: const Color(0xFF79747E),
      disabledColor: const Color(0x61000000),
      focusColor: const Color(0x1F000000),
      highlightColor: const Color(0x66BCBCBC),
      hintColor: const Color(0x99000000),
      hoverColor: const Color(0x0A000000),
      shadowColor: const Color(0xFF000000),
      splashColor: const Color(0x66C8C8C8),
      unselectedWidgetColor: const Color(0x8A000000),
      visualDensity: VisualDensity.compact,
      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,

      // Text theme configuration
      textTheme: TextTheme(
        displayLarge: TextStyle(
          fontSize: 96,
          fontWeight: FontWeight.w300,
          letterSpacing: -1.5,
          color: Color(0x8A000000),
          fontFamily: 'Roboto',
        ),
        displayMedium: TextStyle(
          fontSize: 60,
          fontWeight: FontWeight.w300,
          letterSpacing: -0.5,
          color: Color(0x8A000000),
          fontFamily: 'Roboto',
        ),
        displaySmall: TextStyle(
          fontSize: 48,
          fontWeight: FontWeight.w400,
          letterSpacing: 0,
          color: Color(0x8A000000),
          fontFamily: 'Roboto',
        ),
        headlineLarge: TextStyle(
          fontSize: 40,
          fontWeight: FontWeight.w400,
          letterSpacing: 0.25,
          color: Color(0x8A000000),
          fontFamily: 'Roboto',
        ),
        headlineMedium: TextStyle(
          fontSize: 34,
          fontWeight: FontWeight.w400,
          letterSpacing: 0.25,
          color: Color(0x8A000000),
          fontFamily: 'Roboto',
        ),
        headlineSmall: TextStyle(
          fontSize: 24,
          fontWeight: FontWeight.w400,
          letterSpacing: 0,
          color: Color(0xDD000000),
          fontFamily: 'Roboto',
        ),
        titleLarge: TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.w500,
          letterSpacing: 0.15,
          color: Color(0xDD000000),
          fontFamily: 'Roboto',
        ),
        titleMedium: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w400,
          letterSpacing: 0.15,
          color: Color(0xDD000000),
          fontFamily: 'Roboto',
        ),
        titleSmall: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w500,
          letterSpacing: 0.1,
          color: Color(0xFF000000),
          fontFamily: 'Roboto',
        ),
        bodyLarge: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w400,
          letterSpacing: 0.5,
          color: Color(0xDD000000),
          fontFamily: 'Roboto',
        ),
        bodyMedium: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w400,
          letterSpacing: 0.25,
          color: Color(0xDD000000),
          fontFamily: 'Roboto',
        ),
        bodySmall: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w400,
          letterSpacing: 0.4,
          color: Color(0x8A000000),
          fontFamily: 'Roboto',
        ),
        labelLarge: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w500,
          letterSpacing: 1.25,
          color: Color(0xDD000000),
          fontFamily: 'Roboto',
        ),
        labelMedium: TextStyle(
          fontSize: 11,
          fontWeight: FontWeight.w400,
          letterSpacing: 1.5,
          color: Color(0xFF000000),
          fontFamily: 'Roboto',
        ),
        labelSmall: TextStyle(
          fontSize: 10,
          fontWeight: FontWeight.w400,
          letterSpacing: 1.5,
          color: Color(0xFF000000),
          fontFamily: 'Roboto',
        ),
      ),

      // Primary text theme for text on primary color backgrounds
      primaryTextTheme: TextTheme(
        displayLarge: TextStyle(
          color: primaryTextColor,
          decoration: TextDecoration.none,
          decorationColor: primaryTextColor,
          fontFamily: '.AppleSystemUIFont',
          inherit: true,
        ),
        displayMedium: TextStyle(
          color: primaryTextColor,
          decoration: TextDecoration.none,
          decorationColor: primaryTextColor,
          fontFamily: '.AppleSystemUIFont',
          inherit: true,
        ),
        displaySmall: TextStyle(
          color: primaryTextColor,
          decoration: TextDecoration.none,
          decorationColor: primaryTextColor,
          fontFamily: '.AppleSystemUIFont',
          inherit: true,
        ),
        headlineLarge: TextStyle(
          color: primaryTextColor,
          decoration: TextDecoration.none,
          decorationColor: primaryTextColor,
          fontFamily: '.AppleSystemUIFont',
          inherit: true,
        ),
        headlineMedium: TextStyle(
          color: primaryTextColor,
          decoration: TextDecoration.none,
          decorationColor: primaryTextColor,
          fontFamily: '.AppleSystemUIFont',
          inherit: true,
        ),
        headlineSmall: TextStyle(
          color: primaryTextColor,
          decoration: TextDecoration.none,
          decorationColor: primaryTextColor,
          fontFamily: '.AppleSystemUIFont',
          inherit: true,
        ),
        titleLarge: TextStyle(
          color: primaryTextColor,
          decoration: TextDecoration.none,
          decorationColor: primaryTextColor,
          fontFamily: '.AppleSystemUIFont',
          inherit: true,
        ),
        titleMedium: TextStyle(
          color: primaryTextColor,
          decoration: TextDecoration.none,
          decorationColor: primaryTextColor,
          fontFamily: '.AppleSystemUIFont',
          inherit: true,
        ),
        titleSmall: TextStyle(
          color: primaryTextColor,
          decoration: TextDecoration.none,
          decorationColor: primaryTextColor,
          fontFamily: '.AppleSystemUIFont',
          inherit: true,
        ),
        bodyLarge: TextStyle(
          color: primaryTextColor,
          decoration: TextDecoration.none,
          decorationColor: primaryTextColor,
          fontFamily: '.AppleSystemUIFont',
          inherit: true,
        ),
        bodyMedium: TextStyle(
          color: primaryTextColor,
          decoration: TextDecoration.none,
          decorationColor: primaryTextColor,
          fontFamily: '.AppleSystemUIFont',
          inherit: true,
        ),
        bodySmall: TextStyle(
          color: primaryTextColor,
          decoration: TextDecoration.none,
          decorationColor: primaryTextColor,
          fontFamily: '.AppleSystemUIFont',
          inherit: true,
        ),
        labelLarge: TextStyle(
          color: primaryTextColor,
          decoration: TextDecoration.none,
          decorationColor: primaryTextColor,
          fontFamily: '.AppleSystemUIFont',
          inherit: true,
        ),
        labelMedium: TextStyle(
          color: primaryTextColor,
          decoration: TextDecoration.none,
          decorationColor: primaryTextColor,
          fontFamily: '.AppleSystemUIFont',
          inherit: true,
        ),
        labelSmall: TextStyle(
          color: primaryTextColor,
          decoration: TextDecoration.none,
          decorationColor: primaryTextColor,
          fontFamily: '.AppleSystemUIFont',
          inherit: true,
        ),
      ),

      // Icon themes
      iconTheme: const IconThemeData(color: Color(0xDD000000)),
      primaryIconTheme: const IconThemeData(color: Color(0xFFFFFFFF)),

      // Button theme
      buttonTheme: ButtonThemeData(
        alignedDropdown: false,
        height: 36,
        minWidth: 88,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(2)),
        layoutBehavior: ButtonBarLayoutBehavior.padded,
        textTheme: ButtonTextTheme.normal,
        colorScheme: colorScheme,
      ),

      // Input decoration theme
      inputDecorationTheme: InputDecorationTheme(
        alignLabelWithHint: false,
        filled: false,
        floatingLabelAlignment: FloatingLabelAlignment.start,
        floatingLabelBehavior: FloatingLabelBehavior.auto,
        isCollapsed: false,
        isDense: false,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: const BorderSide(color: Color(0xFF000000), width: 1),
          gapPadding: 4,
        ),
      ),
    );
  }

  @override
  ThemeData get darkTheme {
    const darkPrimaryColor = Color(0xFFD0BCFF);
    const darkSurfaceColor = Color(0xFF141218);
    const darkOnSurfaceColor = Color(0xFFE6E0E9);
    const darkErrorColor = Color(0xFFF2B8B5);
    const darkOnErrorColor = Color(0xFF601410);
    const primaryTextColor = Color(0xFFE6E0E9);

    final colorScheme = ColorScheme.dark(
      brightness: Brightness.dark,
      primary: darkPrimaryColor,
      onPrimary: const Color(0xFF381E72),
      primaryContainer: const Color(0xFF4F378B),
      onPrimaryContainer: const Color(0xFFEADDFF),
      secondary: const Color(0xFFCCC2DC),
      onSecondary: const Color(0xFF332D41),
      secondaryContainer: const Color(0xFF4A4458),
      onSecondaryContainer: const Color(0xFFE8DEF8),
      tertiary: const Color(0xFFEFB8C8),
      onTertiary: const Color(0xFF492532),
      tertiaryContainer: const Color(0xFF633B48),
      onTertiaryContainer: const Color(0xFFFFD8E4),
      error: darkErrorColor,
      onError: darkOnErrorColor,
      errorContainer: const Color(0xFF8C1D18),
      onErrorContainer: const Color(0xFFF9DEDC),
      surface: darkSurfaceColor,
      onSurface: darkOnSurfaceColor,
      onSurfaceVariant: const Color(0xFFCAC4D0),
      outline: const Color(0xFF938F99),
      outlineVariant: const Color(0xFF49454F),
      shadow: const Color(0xFF000000),
      scrim: const Color(0xFF000000),
      inverseSurface: const Color(0xFFE6E0E9),
      onInverseSurface: const Color(0xFF322F35),
      inversePrimary: const Color(0xFF6750A4),
      surfaceTint: darkPrimaryColor,
      surfaceBright: const Color(0xFF3B383E),
      surfaceContainer: const Color(0xFF211F26),
      surfaceContainerHigh: const Color(0xFF2B2930),
      surfaceContainerHighest: const Color(0xFF36343B),
      surfaceContainerLow: const Color(0xFF1D1B20),
      surfaceContainerLowest: const Color(0xFF0F0D13),
      surfaceDim: const Color(0xFF141218),
    );

    return ThemeData(
      useMaterial3: true,
      colorScheme: colorScheme,
      scaffoldBackgroundColor: darkSurfaceColor,
      canvasColor: darkSurfaceColor,
      cardColor: darkSurfaceColor,
      dividerColor: const Color(0xFF938F99),
      disabledColor: const Color(0x62FFFFFF),
      focusColor: const Color(0x1FFFFFFF),
      highlightColor: const Color(0x40CCCCCC),
      hintColor: const Color(0x99FFFFFF),
      hoverColor: const Color(0x0AFFFFFF),
      shadowColor: const Color(0xFF000000),
      splashColor: const Color(0x40CCCCCC),
      unselectedWidgetColor: const Color(0xB3FFFFFF),
      visualDensity: VisualDensity.compact,
      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,

      // Text theme configuration for dark theme
      textTheme: TextTheme(
        displayLarge: TextStyle(
          fontSize: 96,
          fontWeight: FontWeight.w300,
          letterSpacing: -1.5,
          color: Color(0xB3FFFFFF),
          fontFamily: 'Roboto',
        ),
        displayMedium: TextStyle(
          fontSize: 60,
          fontWeight: FontWeight.w300,
          letterSpacing: -0.5,
          color: Color(0xB3FFFFFF),
          fontFamily: 'Roboto',
        ),
        displaySmall: TextStyle(
          fontSize: 48,
          fontWeight: FontWeight.w400,
          letterSpacing: 0,
          color: Color(0xB3FFFFFF),
          fontFamily: 'Roboto',
        ),
        headlineLarge: TextStyle(
          fontSize: 40,
          fontWeight: FontWeight.w400,
          letterSpacing: 0.25,
          color: Color(0xB3FFFFFF),
          fontFamily: 'Roboto',
        ),
        headlineMedium: TextStyle(
          fontSize: 34,
          fontWeight: FontWeight.w400,
          letterSpacing: 0.25,
          color: Color(0xB3FFFFFF),
          fontFamily: 'Roboto',
        ),
        headlineSmall: TextStyle(
          fontSize: 24,
          fontWeight: FontWeight.w400,
          letterSpacing: 0,
          color: Color(0xFFFFFFFF),
          fontFamily: 'Roboto',
        ),
        titleLarge: TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.w500,
          letterSpacing: 0.15,
          color: Color(0xFFFFFFFF),
          fontFamily: 'Roboto',
        ),
        titleMedium: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w400,
          letterSpacing: 0.15,
          color: Color(0xFFFFFFFF),
          fontFamily: 'Roboto',
        ),
        titleSmall: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w500,
          letterSpacing: 0.1,
          color: Color(0xFFFFFFFF),
          fontFamily: 'Roboto',
        ),
        bodyLarge: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w400,
          letterSpacing: 0.5,
          color: Color(0xFFFFFFFF),
          fontFamily: 'Roboto',
        ),
        bodyMedium: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w400,
          letterSpacing: 0.25,
          color: Color(0xFFFFFFFF),
          fontFamily: 'Roboto',
        ),
        bodySmall: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w400,
          letterSpacing: 0.4,
          color: Color(0xB3FFFFFF),
          fontFamily: 'Roboto',
        ),
        labelLarge: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w500,
          letterSpacing: 1.25,
          color: Color(0xFFFFFFFF),
          fontFamily: 'Roboto',
        ),
        labelMedium: TextStyle(
          fontSize: 11,
          fontWeight: FontWeight.w400,
          letterSpacing: 1.5,
          color: Color(0xFFFFFFFF),
          fontFamily: 'Roboto',
        ),
        labelSmall: TextStyle(
          fontSize: 10,
          fontWeight: FontWeight.w400,
          letterSpacing: 1.5,
          color: Color(0xFFFFFFFF),
          fontFamily: 'Roboto',
        ),
      ),

      // Primary text theme for text on primary color backgrounds (dark theme)
      primaryTextTheme: TextTheme(
        displayLarge: TextStyle(
          color: primaryTextColor,
          decoration: TextDecoration.none,
          decorationColor: primaryTextColor,
          fontFamily: '.AppleSystemUIFont',
          inherit: true,
        ),
        displayMedium: TextStyle(
          color: primaryTextColor,
          decoration: TextDecoration.none,
          decorationColor: primaryTextColor,
          fontFamily: '.AppleSystemUIFont',
          inherit: true,
        ),
        displaySmall: TextStyle(
          color: primaryTextColor,
          decoration: TextDecoration.none,
          decorationColor: primaryTextColor,
          fontFamily: '.AppleSystemUIFont',
          inherit: true,
        ),
        headlineLarge: TextStyle(
          color: primaryTextColor,
          decoration: TextDecoration.none,
          decorationColor: primaryTextColor,
          fontFamily: '.AppleSystemUIFont',
          inherit: true,
        ),
        headlineMedium: TextStyle(
          color: primaryTextColor,
          decoration: TextDecoration.none,
          decorationColor: primaryTextColor,
          fontFamily: '.AppleSystemUIFont',
          inherit: true,
        ),
        headlineSmall: TextStyle(
          color: primaryTextColor,
          decoration: TextDecoration.none,
          decorationColor: primaryTextColor,
          fontFamily: '.AppleSystemUIFont',
          inherit: true,
        ),
        titleLarge: TextStyle(
          color: primaryTextColor,
          decoration: TextDecoration.none,
          decorationColor: primaryTextColor,
          fontFamily: '.AppleSystemUIFont',
          inherit: true,
        ),
        titleMedium: TextStyle(
          color: primaryTextColor,
          decoration: TextDecoration.none,
          decorationColor: primaryTextColor,
          fontFamily: '.AppleSystemUIFont',
          inherit: true,
        ),
        titleSmall: TextStyle(
          color: primaryTextColor,
          decoration: TextDecoration.none,
          decorationColor: primaryTextColor,
          fontFamily: '.AppleSystemUIFont',
          inherit: true,
        ),
        bodyLarge: TextStyle(
          color: primaryTextColor,
          decoration: TextDecoration.none,
          decorationColor: primaryTextColor,
          fontFamily: '.AppleSystemUIFont',
          inherit: true,
        ),
        bodyMedium: TextStyle(
          color: primaryTextColor,
          decoration: TextDecoration.none,
          decorationColor: primaryTextColor,
          fontFamily: '.AppleSystemUIFont',
          inherit: true,
        ),
        bodySmall: TextStyle(
          color: primaryTextColor,
          decoration: TextDecoration.none,
          decorationColor: primaryTextColor,
          fontFamily: '.AppleSystemUIFont',
          inherit: true,
        ),
        labelLarge: TextStyle(
          color: primaryTextColor,
          decoration: TextDecoration.none,
          decorationColor: primaryTextColor,
          fontFamily: '.AppleSystemUIFont',
          inherit: true,
        ),
        labelMedium: TextStyle(
          color: primaryTextColor,
          decoration: TextDecoration.none,
          decorationColor: primaryTextColor,
          fontFamily: '.AppleSystemUIFont',
          inherit: true,
        ),
        labelSmall: TextStyle(
          color: primaryTextColor,
          decoration: TextDecoration.none,
          decorationColor: primaryTextColor,
          fontFamily: '.AppleSystemUIFont',
          inherit: true,
        ),
      ),

      // Icon themes for dark theme
      iconTheme: const IconThemeData(color: Color(0xFFE6E0E9)),
      primaryIconTheme: const IconThemeData(color: Color(0xFFFFFFFF)),

      // Button theme for dark theme
      buttonTheme: ButtonThemeData(
        alignedDropdown: false,
        height: 36,
        minWidth: 88,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(2)),
        layoutBehavior: ButtonBarLayoutBehavior.padded,
        textTheme: ButtonTextTheme.normal,
        colorScheme: colorScheme,
      ),

      // Input decoration theme for dark theme
      inputDecorationTheme: InputDecorationTheme(
        alignLabelWithHint: false,
        filled: false,
        floatingLabelAlignment: FloatingLabelAlignment.start,
        floatingLabelBehavior: FloatingLabelBehavior.auto,
        isCollapsed: false,
        isDense: false,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: const BorderSide(color: Color(0xFF938F99), width: 1),
          gapPadding: 4,
        ),
      ),
    );
  }
}
