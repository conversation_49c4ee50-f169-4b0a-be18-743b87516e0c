import 'package:flutter/material.dart';

class ListItemCardHeader extends StatelessWidget {
  const ListItemCardHeader({
    super.key,
    required this.orderNumber,
    required this.nameField,
    this.iconButton,
  });

  final int orderNumber;
  final Widget nameField;
  final Widget? iconButton;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            border: Border.all(color: Theme.of(context).colorScheme.outline),
            shape: BoxShape.circle,
          ),
          child: Text(orderNumber.toString()),
        ),
        const SizedBox(width: 8),
        Expanded(child: nameField),
        if (iconButton != null) iconButton!,
      ],
    );
  }
}
